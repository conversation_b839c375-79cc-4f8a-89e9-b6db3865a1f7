from dash import clientside_callback, ClientsideFunction, Output, Input, State
import json

# Use for toggling password. Login page
clientside_callback(
    ClientsideFunction(
        namespace='clientside',
        function_name='toggle_password'
    ),
    Output(component_id='id-login-passwd-main', component_property='value'),
    Input('toggle-password', 'n_clicks'),
    prevent_initial_call=True
)

# Version Page
clientside_callback(
    ClientsideFunction(
        namespace='clientside',
        function_name='filter_table'
    ),
    Output("id-version-bugs-table", "className"),
    Input("id-version-bugs-table", "id"),
)

# Source:  https://codepen.io/geoffgraham/pen/yLywVbW
clientside_callback(
    """

    function(value) {
    const FULL_DASH_ARRAY = 283;
    const WARNING_THRESHOLD = 90;
    const ALERT_THRESHOLD = 30;

    const COLOR_CODES = {
      info: {
        color: "green"
      },
      warning: {
        color: "orange",
        threshold: WARNING_THRESHOLD
      },
      alert: {
        color: "red",
        threshold: ALERT_THRESHOLD
      }
    };

        const TIME_LIMIT = 180;

        let remainingPathColor = COLOR_CODES.info.color;;
        console.log(remainingPathColor);
        document.getElementById(value).innerHTML = `
        <div class="base-timer">
          <svg class="base-timer__svg" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <g class="base-timer__circle">
              <circle class="base-timer__path-elapsed" cx="50" cy="50" r="45" />
              <path
                id="base-timer-path-remaining"
                stroke-dasharray="283"
                class="base-timer__path-remaining ${remainingPathColor}"
                d="
                  M 50, 50
                  m -45, 0
                  a 45,45 0 1,0 90,0
                  a 45,45 0 1,0 -90,0
                "
              ></path>
            </g>
          </svg>
          <span id="base-timer-label" class="base-timer__label">
            ${formatTimeLeft(TIME_LIMIT)}
          </span>
        </div>
        `;
        startTimer();
    };

    function onTimesUp() {
     clearInterval(timerInterval);
    }

    function formatTimeLeft(time) {
        const minutes = Math.floor(time / 60);
        let seconds = time % 60;
        if (seconds < 10) {
            seconds = `0${seconds}`;
        }
        return `${minutes}:${seconds}`;
    };

    function startTimer() {
        let timePassed = 0;
        const TIME_LIMIT = 180;
        timerInterval = setInterval(() => {
            // The amount of time passed increments by one
            timePassed = timePassed += 1;
            timeLeft = TIME_LIMIT - timePassed;

            // The time left label is updated
            document.getElementById("base-timer-label").innerHTML = formatTimeLeft(timeLeft);
            setCircleDasharray();
            setRemainingPathColor(timeLeft);
            if (timeLeft === 0) {
             onTimesUp();
            }
        }, 1000);
    };


    // Divides time left by the defined time limit.
    function calculateTimeFraction() {
      const TIME_LIMIT = 180;
      const rawTimeFraction = timeLeft / TIME_LIMIT;
      return rawTimeFraction - (1 / TIME_LIMIT) * (1 - rawTimeFraction);
    };

    // Update the dasharray value as time passes, starting with 283
    function setCircleDasharray() {
    const FULL_DASH_ARRAY = 283;
      const circleDasharray = `${(
        calculateTimeFraction() * FULL_DASH_ARRAY
      ).toFixed(0)} 283`;
      document
        .getElementById("base-timer-path-remaining")
        .setAttribute("stroke-dasharray", circleDasharray);
    };

    function setRemainingPathColor(timeLeft) {
    const WARNING_THRESHOLD = 90;
    const ALERT_THRESHOLD = 30;
    const COLOR_CODES = {
      info: {
        color: "green"
      },
      warning: {
        color: "orange",
        threshold: WARNING_THRESHOLD
      },
      alert: {
        color: "red",
        threshold: ALERT_THRESHOLD
      }
    };
  const { alert, warning, info } = COLOR_CODES;
  if (timeLeft <= alert.threshold) {
    document
      .getElementById("base-timer-path-remaining")
      .classList.remove(warning.color);
    document
      .getElementById("base-timer-path-remaining")
      .classList.add(alert.color);
  } else if (timeLeft <= warning.threshold) {
    document
      .getElementById("base-timer-path-remaining")
      .classList.remove(info.color);
    document
      .getElementById("base-timer-path-remaining")
      .classList.add(warning.color);
  }
}

    """,
    Output("in-component1", "value"),
    Input('dash_app', 'id')
)

# Home page
clientside_callback(
    """
    function(name) {
        const now = new Date();
        const hour = now.getHours();
        let greeting;
        elem = document.getElementById(name).innerHTML
        console.log("Javascript invoked");
        console.log(elem);
        if (navigator.languages && navigator.languages.length) {
            console.log(navigator.languages[0])
            switch (navigator.languages[0]) {
                case 'en-IN':
                    if (hour < 4) {
                      greeting = "शुभ बिहान!";
                    } else if (hour < 12) {
                      greeting = "शुभ प्रभात!";
                    } else if (hour < 16) {
                      greeting = "शुभ दोपहर!";
                    } else if (hour < 20) {
                      greeting = "शुभ संध्या!";
                    } else {
                      greeting = "शुभ रात्रि!";
                    }
                    break;
                default:
                    if (hour < 12) {
                        greeting = "Good morning!";
                    } else if (hour < 18) {
                      greeting = "Good afternoon!";
                    } else {
                      greeting = "Good evening!";
                    }
            }
            console.log(greeting)
            return greeting + ' ' + elem
        } else {
            return (navigator.userLanguage || navigator.language || navigator.browserLanguage || 'en');
        }
    }
    """,
    Output("id-welcome-user", "children"),
    Input("id-welcome-user", "id")
)

# Modern Navigation - Set active navigation item based on current URL
clientside_callback(
    """
    function(pathname) {
        // Initialize result object
        const result = {};

        // Reset all items to inactive
        document.querySelectorAll('.nav-link, .nav-dropdown-link').forEach(link => {
            const href = link.getAttribute('href');
            const id = link.id;

            if (id) {
                result[id] = '';
            }
        });

        // Find the active link based on current pathname
        document.querySelectorAll('.nav-link, .nav-dropdown-link').forEach(link => {
            const href = link.getAttribute('href');
            const id = link.id;

            if (href && id) {
                // Check if the current pathname matches this link's href
                if (pathname === href) {
                    result[id] = 'active';

                    // If it's a dropdown item, open its parent dropdown
                    const dropdownParent = link.closest('.nav-dropdown');
                    if (dropdownParent && dropdownParent.id) {
                        result[dropdownParent.id] = 'nav-dropdown open';
                    }
                }
            }
        });

        return JSON.stringify(result);
    }
    """,
    Output("active-nav-state", "data"),
    Input("url", "pathname")
)

# Add login-page class to body when on login pages
clientside_callback(
    """
    function(pathname) {
        // Check if the current page is a login page
        const isLoginPage = pathname.includes('login') || pathname.includes('logout');

        // Add or remove the login-page class from the body
        if (isLoginPage) {
            document.body.classList.add('login-page');
        } else {
            document.body.classList.remove('login-page');
        }

        // Return pathname unchanged (this output is just a placeholder)
        return pathname;
    }
    """,
    Output("url", "pathname", allow_duplicate=True),
    Input("url", "pathname"),
    prevent_initial_call=True
)

# Login is now handled by direct form submission to Flask route

# Apply active state to navigation items
clientside_callback(
    """
    function(activeStateJson) {
        if (!activeStateJson) return window.dash_clientside.no_update;

        try {
            const activeState = JSON.parse(activeStateJson);

            // Apply active classes to navigation items
            Object.keys(activeState).forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    if (activeState[id]) {
                        element.className = element.className.split(' ')
                            .filter(c => c !== 'active')
                            .join(' ') + ' ' + activeState[id];
                    }
                }
            });

            return window.dash_clientside.no_update;
        } catch (e) {
            console.error('Error applying active navigation state:', e);
            return window.dash_clientside.no_update;
        }
    }
    """,
    Output("url", "pathname", allow_duplicate=True),
    Input("active-nav-state", "data"),
    prevent_initial_call=True
)
