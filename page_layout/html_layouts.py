import base64
import os
from dash import html, dcc
from dash_iconify import DashIconify

# import dash_daq as daq
# import dash_mantine_components as dmc
# from dash_iconify import DashIconify

from app import current_user, superuser_permission
from data import helper
import dash_svg as svg

def generate_layout(project_list: tuple = (), project_index: int = 1):
    path_name = os.getenv('DASH_BASE_PATH', '/')
    li_children = [
        html.Li(children=[
            html.A(
                children=[html.Span(children=['Home'])],
                href=f"{path_name}"
            ),
            # html.Div(id="socket-connection")
        ], className="top-level-link"),
    ]

    if current_user and current_user.is_authenticated:
        if project_list:
            # if len(project_list) > 5:
            #     parse_projects = project_list[project_index - 1:5 * project_index]
            # else:
            #     parse_projects = project_list
            for key in project_list:
                li_children.append(
                    html.Li(children=[
                        html.A(children=[html.Span(children=[f'{key.upper()}'])], className='mega-menu'),
                        html.Div(children=[
                            html.Div(children=[
                                html.Div(children=[
                                    html.H2(children=["Dashboard"], className="sub-menu-head"),
                                    html.Ul(children=[
                                        html.Li(children=[
                                            html.A("By sprint", href=f"{path_name}{key}/overview", )
                                        ]),
                                        html.Li(children=[
                                            html.A("By release", href=f"{path_name}{key}/version", )
                                        ]),
                                        html.Li(children=[
                                            html.A("SVN Details", href=f"{path_name}{key}/svn", )
                                        ]),
                                    ], className="sub-menu-lists")
                                ], className="col-md-4 col-lg-4 col-sm-4"),
                                # html.Div(children=[
                                #     html.H2(children=["Time sheets"], className="sub-menu-head"),
                                #     html.Ul(
                                #         children=[
                                #             html.Li(children=[html.A("Month Wise", href=f"/{key}/monthlyts", )])],
                                #         className="sub-menu-lists"),
                                #     html.Ul(
                                #         children=[
                                #             html.Li(children=[html.A("By Resource", href=f"/{key}/resourcets", )])],
                                #         className="sub-menu-lists"),
                                # ], className="col-md-4 col-lg-4 col-sm-4"),
                                html.Div(children=[
                                    html.H2(children=["Project Specific"], className="sub-menu-head"),
                                    html.Ul(children=[
                                        # html.Li(children=[
                                        #     html.A("Backlog Refinement", href=f"/{key}/refinement", )
                                        # ]),
                                        html.Li(children=[
                                            html.A("SQL Code Review", href=f"{path_name}{key}/SQL Code Review", )
                                        ]),
                                        html.Li(children=[
                                            html.A("WD Timesheet", href=f"{path_name}plat/wdts", )
                                        ]),
                                    ], className="sub-menu-lists")
                                ], className="col-md-4 col-lg-4 col-sm-4"),
                                html.Div(children=[
                                    html.H2(children=["Reports"], className="sub-menu-head"),
                                    html.Ul(children=[
                                        html.Li(
                                            children=[html.A("Description", href=f"{path_name}{key}/description", )]
                                        ),
                                        html.Li(children=[html.A("Aging", href=f"{path_name}{key}/issueaging", )]),
                                        html.Li(children=[html.A("Compliance", href=f"{path_name}{key}/compliance", )]),
                                        html.Li(children=[html.A("Time Log", href=f"{path_name}{key}/timelog", )]),
                                    ], className="sub-menu-lists")
                                ], className="col-md-4 col-lg-4 col-sm-4"),
                                # html.Div(children=[
                                #     html.H2(children=["Reports"], className="sub-menu-head"),
                                #     html.Ul(children=[
                                #         html.Li(children=[
                                #             html.A("Unassigned", href=f"/{key}/unassigned", )
                                #         ])
                                #     ], className="sub-menu-lists")
                                # ], className="col-md-4 col-lg-4 col-sm-4"),
                                # Start New menu

                                # End New menu
                                # html.Div(children=[
                                #     html.H2(children=["Misc."], className="sub-menu-head"),
                                #     html.Ul(children=[
                                #         html.Li(children=[
                                #             html.A("Duplicate JIRA", href=f"/{key}/duplicate", )
                                #         ]),
                                #         html.Li(children=[
                                #             html.A("Initiative Attributes", href=f"/{key}/initattr", )
                                #         ]),
                                #     ], className="sub-menu-lists")
                                # ], className="col-md-4 col-lg-4 col-sm-4"),
                                # Updates to team can be handled via flask-admin interface
                                # html.Div(children=[
                                #     html.H2(children=["Admin"], className="sub-menu-head"),
                                #     html.Ul(children=[
                                #         html.Li(children=[
                                #             html.A("Update Initiative Attributes", href=f"/{key}/updateinitattribs", )
                                #         ]),
                                #         html.Li(children=[
                                #             html.A("Update Teams", href=f"/{key}/updateteams", )
                                #         ]),
                                #     ], className="sub-menu-lists")
                                # ], className="col-md-4 col-lg-4 col-sm-4")
                                # html.Div(plat_li_children),
                            ], className="row"),
                        ], className="sub-menu-block")
                    ], className="top-level-link"),
                )

    # if current_user and not current_user.is_anonymous \
    #         and current_user.id in roles_and_permissions \
    #         and pmo_role in roles_and_permissions[current_user.id]:

    if current_user.is_authenticated and (current_user.is_pmo or current_user.is_superuser):
        li_children.append(
            html.Li(
                children=[
                    # html.A(children=[html.Span(children=['Clocks'])],href="/clocks"),], className="top-level-link"),
                    html.A(children=[html.Span(children=['PMO'])], className="mega-menu"),
                    html.Div(
                        children=[
                            html.Div(
                                children=[
                                    html.Div(
                                        children=[
                                            html.H2(children=["Data Management"], className="sub-menu-head"),
                                            html.Ul(
                                                children=[
                                                    html.Li(children=[
                                                        html.A("Admin", href=f"/admin", target="_blank"),
                                                    ]),
                                                    html.Li(children=[
                                                        html.A("Request Tracker", href=f"{path_name}public/rt"),
                                                    ]),
                                                ], className="sub-menu-lists"
                                            )

                                        ], className="col-md-4 col-lg-4 col-sm-4"
                                    ),
                                    # New Add
                                    html.Div(
                                        children=[
                                            html.H2(children=["Bulk Data Creation"], className="sub-menu-head"),
                                            html.Ul(
                                                children=[
                                                    html.Li(children=[
                                                        html.A("Teams - Plat", href=f"{path_name}plat/teams"),
                                                    ]),
                                                    html.Li(children=[
                                                        html.A(
                                                            "Upload Request Tracker File",
                                                            href=f"{path_name}public/requesttracker"
                                                        ),
                                                    ]),
                                                ], className="sub-menu-lists"
                                            )

                                        ], className="col-md-4 col-lg-4 col-sm-4"
                                    ),
                                    # End New Add
                                    html.Div(children=[
                                        html.H2(children=["Batch Job"], className="sub-menu-head"),
                                        html.Ul(children=[
                                            html.Li(children=[
                                                html.A("Update Estimates", href=f"{path_name}plat/estimates", )
                                            ]),
                                            html.Li(children=[
                                                html.A("Transition Issues", href=f"{path_name}plat/transition", )
                                            ]),
                                            html.Li(children=[
                                                html.A("Create CTs", href=f"{path_name}plat/coretrack", )
                                            ]),
                                            html.Li(children=[
                                                html.A("SVN Info", href=f"{path_name}plat/svndetails", )
                                            ]),

                                        ], className="sub-menu-lists")
                                    ], className="col-md-4 col-lg-4 col-sm-4"),
                                    html.Div(
                                        children=[
                                            html.H2("Status Report", className="sub-menu-head"),
                                            html.Ul(
                                                children=[
                                                    html.Li(children=[
                                                        html.A("Weekly Status Report - Plat",
                                                               href=f"{path_name}plat/weekly_report"),
                                                    ])
                                                ], className="sub-menu-lists"
                                            ),
                                        ]
                                    ),
                                    html.Div(
                                        children=[
                                            html.H2("Release Notes", className="sub-menu-head"),
                                            # html.Li(children=[
                                            #     html.A("Release Notes", href=f"/{key}/releasenotes", )
                                            # ]),
                                            html.Ul(
                                                children=[
                                                    html.Li(children=[
                                                        html.A("Plat Release Notes",
                                                               href=f"{path_name}plat/releasenotes", )
                                                    ]),
                                                ], className="sub-menu-lists"
                                            ),
                                        ]
                                    ),
                                ], className='row')
                        ], className="sub-menu-block"
                    )

                ],
                className="top-level-link"
            )
        )

    # Playground menu
    if current_user.is_authenticated and superuser_permission.can():
        li_children.append(
            html.Li(
                children=[
                    # html.A(children=[html.Span(children=['Clocks'])],href="/clocks"),], className="top-level-link"),
                    html.A(children=[html.Span(children=['R&D'])], className="mega-menu"),
                    html.Div(
                        children=[
                            html.Div(
                                children=[
                                    html.Div(children=[
                                        html.H2(children=["C4 Diagrams"], className="sub-menu-head"),
                                        html.Ul(
                                            children=[
                                                helper.generate_header_leaf_node("Credit Card",
                                                                                 f"{path_name}r&d/creditcard"),
                                                helper.generate_header_leaf_node("Mortgages",
                                                                                 f"{path_name}r&d/mortgages"),
                                            ],
                                            className="sub-menu-lists"
                                        )

                                    ], className="col-md-4 col-lg-4 col-sm-4"),
                                    html.Div(children=[
                                        html.H2(children=["Research"], className="sub-menu-head"),
                                        html.Ul(
                                            children=[
                                                helper.generate_header_leaf_node("chatGPT", f"{path_name}r&d/chatgpt"),
                                                helper.generate_header_leaf_node("Service Availability",
                                                                                 f"{path_name}r&d/service"),
                                            ],
                                            className="sub-menu-lists"
                                        )

                                    ], className="col-md-4 col-lg-4 col-sm-4"),

                                ], className='row')
                        ], className="sub-menu-block"
                    )

                ],
                className="top-level-link"
            )
        )
    # End Playground

    return li_children


# Save your SVG content to a file or encode it as base64
svg_content = '''<svg width="60" height="60" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="120" height="120" rx="20" fill="#1890FF"/>
    <path d="M30 60C30 43.4315 43.4315 30 60 30V30C76.5685 30 90 43.4315 90 60V60C90 76.5685 76.5685 90 60 90V90C43.4315 90 30 76.5685 30 60V60Z" stroke="white" stroke-width="8"/>
    <path d="M60 45V75" stroke="white" stroke-width="8" stroke-linecap="round"/>
    <path d="M45 60H75" stroke="white" stroke-width="8" stroke-linecap="round"/>
</svg>'''

# Encode SVG as base64
svg_base64 = base64.b64encode(svg_content.encode()).decode()
svg_data_uri = f"data:image/svg+xml;base64,{svg_base64}"


# Login screen
login = html.Div(
    children=[
        # html.Div(children=[], className="text-center"),
        # dcc.Store(id="id-store-projects", storage_type='session'),
        dcc.Location(id='url_login', refresh=True),
        # dcc.Store(id="public-key", storage_type="session"),
        # dcc.Store("encrypted-token", storage_type="session"),
        # dcc.Input(id="encrypted-token", value=''),
        html.Div(id="notifications-container"),
        html.Div(id="id-login-username", className="hidden"),
        html.Ul(
            children=[html.Li(children=[
                html.Div(
                    children=[
                        html.Div(
                            children=[
                                dcc.Input(
                                    type='email', id="id-login-main",
                                    placeholder="O365 email id",
                                    className="input-creds",
                                    debounce=True,
                                    persistence=True,
                                    value=''
                                ),
                                html.Span(className='focus-input-creds'),
                                html.Span(children=[html.I(className="fa fa-envelope")],
                                          className="symbol-input-creds"),
                            ],
                            className='wrap-input-creds', tabIndex="1"
                        ),
                    ],
                ),
            ]), ],
            className="login-container-ul"
        ),
        html.Div(
            html.Ul(
                children=[
                    html.Li(children=[
                        dcc.Input(
                            type='password', id="id-login-passwd-main", placeholder="Password",
                            className="input-creds", debounce=True, persistence=True,
                            value=''
                        ),
                        html.Span(className='focus-input-creds'),
                        html.Span(children=[html.I(className="fa fa-lock"), ], className="symbol-input-creds"),
                        html.Span(
                            children=[
                                html.I(className="fa fa-eye", id="toggle-password")
                            ],
                            className="toggle-password-icon"
                        ),
                    ],
                        className='wrap-input-creds'
                    ),

                ],
                className="login-container-ul"
            ),
        ),
        html.Div(
            children=[
                html.Button(
                    id="id-button-login-main", children="login",
                    className="format_button", n_clicks=0, autoFocus='AUTOFOCUS'
                ),
            ],
            className="container-login-button"
        ),
        # html.Div(id="socket-conn")
    ], className="container1x2 pt-5"
)

login_2fa = html.Div(
    children=[
        html.Div(
            children=[
                dcc.Input(id='in-component1', type='text', style={'display': 'none'}),
                html.Div(id="dash_app", style={'display': 'none'}),
                # New Test for JavaScript
                html.Div(id='out-component', style={'display': 'none'}),

                # dcc.Input(id='in-component2', type='text'),
                # html.Button('Calculate', id='calculate-button')
                # End Test
            ]
        ),
        html.Div(
            children=[
                dcc.Location(id="url_login_2fa"),
                html.Div(
                    children=[
                        html.Div(
                            children=[
                                html.H2("Two Factor Authentication"),
                                html.H4("Google Authenticator")
                            ], className="jumbotron-custom text-center round-border"
                        ),
                        html.Div(
                            children=[
                                html.Ul(
                                    children=[
                                        html.Li(
                                            children=[
                                                html.Label("Enter PIN", className="add-margin-right-5px"),
                                                dcc.Input(
                                                    id='id-input-token',
                                                    autoFocus=True,
                                                    debounce=True,
                                                    type="text",
                                                    value='',
                                                    maxLength=6,
                                                    pattern="[0-9]+",
                                                    n_submit=0
                                                ),
                                                html.Div(id="id-validate-otp-token")
                                            ],
                                            className='wrap-input-creds'
                                        ),
                                    ],
                                    className="login-container-ul"
                                ),
                            ], className="text-center"
                        ),
                        html.Div(
                            children=[
                                html.Button(
                                    id="id-button-token",
                                    children="Authenticate",
                                    className="format_button add-margin-right-20px",
                                    n_clicks=0
                                ),
                                html.Button(id="id-button-reset-pin", children="Reset PIN", className="format_button",
                                            n_clicks=0),
                            ],
                            className="container-login-button"
                        ),
                        html.Div(id="id-instructions", className="add-margin-top-20px")
                    ],
                ),

            ],
            className="container-2fa"
        ),
    ], className="main-page-content"
)

# Failed Login
failed = html.Div(
    [html.Div([html.H2('Log in Failed. Please try again.'),
               html.Br(),
               html.Div([login]),
               dcc.Link('Home', href='/')
               ])  # end div
     ])

# logout
logout = html.Div(
    [html.Div(html.H2('You have been logged out - Please login')),
     html.Br(),
     dcc.Link('Home', href='/login')
     ]
)


def html_header(session):
    image_element = html.Img(
                            src='data:image/png;base64,{}'.format(session.get('encoded_image', '')),
                            className="avatar",
                            id="id-logout-menu-test",
                            n_clicks=0
                        ) if session.get('encoded_image_status') == 200 else html.Img(
                            src=session['avatarUrls16x16'] if session.get('avatarUrls16x16') else None,
                            className="avatar",
                            id="id-logout-menu",
                            n_clicks=0,
                        )

    page_header = html.Header(
        children=[
            # daq.NumericInput(
            #     id="id-my-project-count", value=1,
            #     max=math.ceil(len(session['projects'] if session.get('projects') else ['plat']) / 5),
            #     size=50,
            #     className="ic format-numeric-input",
            #     style={'left': '15px', 'top': '15px', 'z-index': '20'}
            # ),
            # dcc.Store(
            #     id="id-session-project", storage_type="local",
            #     data=session['projects'] if session.get('projects') else ['plat']
            # ),
            html.Nav(children=[
                html.A(children=[
                    html.Span(className="line"),
                    html.Span(className="line"),
                    html.Span(className="line")
                ], className="ic menu", tabIndex="1",
                ),

                html.A(
                    children=[
                        image_element,
                    ],
                    className="ic close",
                    n_clicks=0,
                    href=f"{os.getenv('DASH_BASE_PATH', '/')}logout"
                ),
                # html.Ul(
                #     className="ic close popup-menu",
                #     id="dropdown-menu",
                #     children=[
                #         html.Li(
                #            children=[
                #                DashIconify(icon="gg:profile"),
                #                html.Span("Profile")
                #            ],
                #             className="format-li"
                #         ),
                #         html.Li(
                #             children=[
                #                 DashIconify(icon="uil:setting"),
                #                 html.Span("Settings")
                #             ],
                #         ),
                #         html.Li(
                #             children=[
                #                 DashIconify(icon="mynaui:logout"),
                #                 html.A(
                #                     children=[
                #                         "Logout"
                #                     ],
                #                     href=f"{os.getenv('DASH_BASE_PATH', '/')}logout"
                #                 )
                #             ]
                #
                #         ),
                #     ],
                # ),
                html.Ul(
                    children=generate_layout(session['projects'] if session.get('projects') else ['plat']),
                    className="main-nav",
                    id="id-regen-header"
                ),
            ], role='navigation')
        ],
        className='dark'
    )
    return page_header
