from sqlalchemy_utils import EmailType, StringEncryptedType

from dbmodels import Base, CacheyCIText, Role, User, Permission
from dbmodels.base import TableName, TableNameCamelCase, HasPrivate, TimestampMixin
from sqlalchemy import Column, INTEGER, ForeignKey, Unicode
from sqlalchemy.orm import Mapped, mapped_column


def get_key():
    return 'dynamic-key'


class UserRole(Base, TableNameCamelCase):
    use_snake_case = True
    # Explicitly set the table name to ensure it's 'user_role' not 'userrole'
    __tablename__ = 'user_role'

    emailAddress: Mapped[EmailType] = mapped_column(
        EmailType,
        ForeignKey(User.emailAddress),
        primary_key=True
    )
    role_id = Column(
        INTEGER, ForeignKey(Role.id), primary_key=True, nullable=False
    )
    __table_args__ = (
        {'schema': 'public'}
    )


class RolePermissions(Base, TableNameCamelCase):
    use_snake_case = True
    # Explicitly set the table name to ensure it's 'role_permissions' not 'rolepermissions'
    __tablename__ = 'role_permissions'

    role_id: Mapped[int] = mapped_column(
        INTEGER, ForeignKey(Role.id), primary_key=True, nullable=False
    )
    permission_id: Mapped[int] = mapped_column(
        INTEGER, ForeignKey(Permission.id), primary_key=True, nullable=False
    )
    __table_args__ = (
        {'schema': 'public'}
    )


class UserToken(HasPrivate, TimestampMixin, Base):
    use_snake_case = True

    emailAddress: Mapped[EmailType] = mapped_column(
        EmailType,
        ForeignKey(User.emailAddress),
        primary_key=True
    )
    qr_code: Mapped[str] = mapped_column(
        StringEncryptedType(Unicode, get_key)
    )

