import re
from datetime import datetime
from typing import Type, Union, Any

from packaging import version
from sqlalchemy import __version__ as sa_version, MetaData, Boolean, func

from citext import CIText
from sqlalchemy.dialects.postgresql import JSONB, TIMESTAMP

from sqlalchemy.ext.asyncio import AsyncAttrs
from sqlalchemy.orm import (
    object_mapper, declarative_base, has_inherited_table,
    declared_attr, DeclarativeMeta, DeclarativeBase, mapped_column, Mapped
)

naming_convention = {
    "ix": 'ix_%(column_0_label)s',
    "uq": "uq_%(table_name)s_%(column_0_name)s",
    "ck": "ck_%(table_name)s_%(constraint_name)s",
    "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
    "pk": "pk_%(table_name)s"
}

metadata = MetaData(naming_convention=naming_convention)


class HasPrivate:
    """Mixin that identifies a class as having private entities"""

    public = mapped_column(Boolean, nullable=False)


class TimestampMixin:
    created_at: Mapped[datetime] = mapped_column(default=func.now())
    updated_at: Mapped[datetime]

class TableName(object):
    """
    This class is passed to declarative_base to auto generate __tablename__ for each model class
    """

    @declared_attr
    def __tablename__(cls: Type['TableName']) -> Union[str, None]:
        """
        Dynamically generates the table name based on the class name in snake_case format.
        Returns:

        """
        if has_inherited_table(cls):
            return None
        return cls.__name__.lower()


class TableNameCamelCase(object):
    """
    This class is passed to declarative_base to auto generate __tablename__ for each model class
    """

    @declared_attr
    def __tablename__(cls: Type['TableName']):
        if has_inherited_table(cls):
            return None
        # Improved regex to properly handle camel case to snake case conversion
        # This will correctly convert UserRole to user_role instead of userrole
        split_camel_case = re.sub(r'(?<!^)(?=[A-Z])', '_', cls.__name__).lower()
        return split_camel_case


if version.parse(sa_version) >= version.parse('2.0'):
    class Base(AsyncAttrs, DeclarativeBase):
        """
        The base class of the model class hierarchy. Maintains a catalog of classes and tables relative to the base.
        Adds an automatic ``__repr__`` method to *all* subclasses of ``Base``.
        This ``__repr__`` will represent values as::
        ClassName(key_1=value_1, key_2=value_2, ..., key_n=value_n)
        where ``key_1..key_n`` are the key columns of the mapped table with the corresponding values.
        """
        __abstract__ = True
        use_snake_case = False
        metadata = metadata

        type_annotation_map = {
            dict[str, Any]: JSONB,
            datetime: TIMESTAMP(timezone=True),
        }

        @declared_attr
        def __tablename__(cls) -> object:
            if '__tablename__' in cls.__dict__:
                return cls.__dict__['__tablename__']

            # Convert CamelCase to snake_case
            if cls.use_snake_case:
                split_camel_case = "_".join(
                    re.sub('([A-Z][a-z]+)', r' \1', re.sub('([A-Z]+)', r' \1', cls.__name__)).split()
                ).lower()
                return split_camel_case
            else:
                return cls.__name__.lower()

        def __repr__(self):
            mapper = object_mapper(self)
            items = [(p.key, getattr(self, p.key))
                     for p in [
                         mapper.get_property_by_column(c) for c in mapper.columns]]
            return "{0}({1})".format(
                self.__class__.__name__,
                ', '.join(['{0}={1!r}'.format(*_) for _ in items]))
else:
    class RepresentableBase(AsyncAttrs):
        __abstract__ = True

        use_snake_case = False

        @declared_attr
        def __tablename__(cls) -> object:
            if '__tablename__' in cls.__dict__:
                return cls.__dict__['__tablename__']

            # Convert CamelCase to snake_case
            if cls.use_snake_case:
                split_camel_case = "_".join(
                    re.sub('([A-Z][a-z]+)', r' \1', re.sub('([A-Z]+)', r' \1', cls.__name__)).split()
                ).lower()
                return split_camel_case
            else:
                return cls.__name__.lower()

        def __repr__(self):
            mapper = object_mapper(self)
            items = [(p.key, getattr(self, p.key))
                     for p in [
                         mapper.get_property_by_column(c) for c in mapper.columns]]
            return "{0}({1})".format(
                self.__class__.__name__,
                ', '.join(['{0}={1!r}'.format(*_) for _ in items]))


    Base = declarative_base(cls=RepresentableBase, metadata=metadata)


class CacheyCIText(CIText):
    # Workaround for https://github.com/mahmoudimus/sqlalchemy-citext/issues/25
    # Can remove when that issue is fixed
    cache_ok = True
