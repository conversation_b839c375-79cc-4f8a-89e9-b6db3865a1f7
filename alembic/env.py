import re
from logging.config import fileConfig
from typing import Union, Literal, Optional, Iterable

from alembic.autogenerate import render_python_code, comparators, renderers
from alembic.operations import Operations, MigrateOperation, MigrationScript, ops
from alembic.runtime.migration import MigrationContext
from sqlalchemy import engine_from_config, text, inspect
from sqlalchemy import pool

from alembic import context, autogenerate, operations
import dbmodels
from sqlalchemy.dialects.postgresql.base import ischema_names
from sqlalchemy.ext.compiler import compiles
from sqlalchemy.sql.ddl import CreateTable, DropTable
from sqlalchemy.sql.schema import SchemaItem, Sequence, UniqueConstraint
from sqlalchemy_utils import database_exists, create_database

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
target_metadata = dbmodels.Base.metadata

# Include specific schemas
# This is for schema_translate_map workaround
# Source: https://alembic.sqlalchemy.org/en/latest/cookbook.html
include_schemas = False


# Get the project name from the environment or config
# current_tenant = config.get_main_option("project_name", "plat")

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.

def include_name(
        name: str | None, type_: str, parent_names: dict[str, str | None]
):
    # print(f"include_name: {type_}:{name}:{target_metadata.schema}")
    # for key, value in parent_names.items():
    #     print(key, value)
    if type_ == "schema":
        print(f"schema={name}")
    if type_ == "table":
        print(f"{name}")
        for key, value in parent_names.items():
            print(key, value)
    # if type_ == "schema":
    #     return name in [None, 'plat', 'public']
    # if type_ == "table":
    #
    #     return (
    #             parent_names["schema_qualified_table_name"] in
    #             target_metadata.tables
    #     )
    # else:
    #     return True
    return True

def include_object(
        _object, name,
        type_: Union[
            Literal["schema"], Literal["table"], Literal["column"], Literal["index"], Literal["unique_constraint"],
            Literal["foreign_key_constraint"]],
        reflected: bool, compare_to: Optional[SchemaItem]
):
    target_schema = get_target_schema()
    if type_ == "table":
        if name == "alembic_version":
            return False

        if _object.schema == target_schema or _object.schema is None:
            return True
        return False
    return True


# Read the -x argument
def get_target_schema():
    x_arg = context.get_x_argument(as_dictionary=True)
    return x_arg.get('tenant', 'public')


# Source: https://hellowac.github.io/alembic-doc-zh/en/api/operations.html
# Start custom sequence
@Operations.register_operation("create_sequence")
class CreateSequenceOp(MigrateOperation):
    """Create a SEQUENCE."""

    def __init__(self, sequence_name, schema=None):
        self.sequence_name = sequence_name
        self.schema = schema

    @classmethod
    def create_sequence(cls, operations, sequence_name, **kw):
        """Issue a "CREATE SEQUENCE" instruction."""

        op = CreateSequenceOp(sequence_name, **kw)
        return operations.invoke(op)

    def reverse(self):
        # only needed to support autogenerate
        return DropSequenceOp(self.sequence_name, schema=self.schema)


@Operations.register_operation("drop_sequence")
class DropSequenceOp(MigrateOperation):
    """Drop a SEQUENCE."""

    def __init__(self, sequence_name, schema=None):
        self.sequence_name = sequence_name
        self.schema = schema

    @classmethod
    def drop_sequence(cls, operations, sequence_name, **kw):
        """Issue a "DROP SEQUENCE" instruction."""

        op = DropSequenceOp(sequence_name, **kw)
        return operations.invoke(op)

    def reverse(self):
        # only needed to support autogenerate
        return CreateSequenceOp(self.sequence_name, schema=self.schema)


@Operations.implementation_for(CreateSequenceOp)
def create_sequence(operations, operation):
    if operation.schema is not None:
        name = "%s.%s" % (operation.schema, operation.sequence_name)
    else:
        name = operation.sequence_name
    operations.execute("CREATE SEQUENCE %s" % name)


@Operations.implementation_for(DropSequenceOp)
def drop_sequence(operations, operation):
    if operation.schema is not None:
        name = "%s.%s" % (operation.schema, operation.sequence_name)
    else:
        name = operation.sequence_name
    operations.execute("DROP SEQUENCE %s" % name)


# Register a comparison function
@comparators.dispatch_for("schema")
def compare_sequences(autogen_context, upgrade_ops, schemas):
    all_conn_sequences = set()

    for sch in schemas:
        # Consider 'None' as the default schema name, usually 'public'
        nspname = autogen_context.dialect.default_schema_name if sch is None else sch

        query = text(
            "SELECT relname FROM pg_class c JOIN pg_namespace n ON n.oid = c.relnamespace WHERE relkind = 'S' AND n.nspname = :nspname")

        results = autogen_context.connection.execute(query, {'nspname': nspname})

        all_conn_sequences.update([
            (sch, row[0]) for row in results
        ])

    # get the collection of Sequence objects we're storing with
    # our MetaData
    metadata_sequences = autogen_context.metadata.info.setdefault(
        "sequences", set())

    # for new names, produce CreateSequenceOp directives
    for sch, name in metadata_sequences.difference(all_conn_sequences):
        upgrade_ops.ops.append(
            CreateSequenceOp(name, schema=sch)
        )

    # for names that are going away, produce DropSequenceOp
    # directives
    for sch, name in all_conn_sequences.difference(metadata_sequences):
        upgrade_ops.ops.append(
            DropSequenceOp(name, schema=sch)
        )


@renderers.dispatch_for(CreateSequenceOp)
def render_create_sequence(autogen_context, op):
    return "op.create_sequence(%r, **%r)" % (
        op.sequence_name,
        {"schema": op.schema}
    )


@renderers.dispatch_for(DropSequenceOp)
def render_drop_sequence(autogen_context, op):
    return "op.drop_sequence(%r, **%r)" % (
        op.sequence_name,
        {"schema": op.schema}
    )


# End custom sequence

# Source: https://github.com/sqlalchemy/alembic/issues/151.
# Not sure about its working.

@compiles(CreateTable)
def _add_if_not_exists(element, compiler, **kw):
    output = compiler.visit_create_table(element, **kw)
    if element.element.info.get("ifexists"):
        output = re.sub(
            "^\s*CREATE TABLE", "CREATE TABLE IF NOT EXISTS", output, re.S)
    return output


@compiles(DropTable)
def _add_if_exists(element, compiler, **kw):
    output = compiler.visit_drop_table(element, **kw)
    if element.element.info.get("ifexists"):
        output = re.sub(
            "^\s*DROP TABLE", "DROP TABLE IF EXISTS", output, re.S)
    return output


def render_item(type_, obj, autogen_context):
    """Apply custom rendering for selected items."""
    table_name = getattr(obj, 'table', None)

    if type_ == "type" and obj.__class__.__module__.startswith("sqlalchemy_utils."):
        autogen_context.imports.add(f"import {obj.__class__.__module__}")
        if hasattr(obj, "choices"):
            return f"{obj.__class__.__module__}.{obj.__class__.__name__}(choices={obj.choices})"
        else:
            return f"{obj.__class__.__module__}.{obj.__class__.__name__}()"

    # Custom rendering for CacheyCIText
    if type_ == "type" and obj.__class__.__module__ == "dbmodels.base" and obj.__class__.__name__ == "CacheyCIText":
        autogen_context.imports.add("import dbmodels.base")
        return f"dbmodels.base.CacheyCIText()"
        # return "sa.types.UserDefinedType(name='public.CITEXT')"

    # Custom rendering for FetchedValue
    # obj.__class__.__module__ == "sqlalchemy.schema"
    # pattern = re.compile(r'FetchedValue')
    # if pattern.search(obj.__class__.__name__):
    #     print(f"match {type_} {obj.__class__.__name__}")

    if type_ == "server_default" and obj.__class__.__name__ == "FetchedValue":
        autogen_context.imports.add("from sqlalchemy.schema import FetchedValue")
        return "FetchedValue()"

    # default rendering for other objects
    # if table_name is not None:
    #     print(f"Type: {type_} Table Name = {table_name.name}, Schema: {table_name.schema}")

    # if table_name is not None and table_name.schema == 'public':
    #     print(f"Table names: {table_name.name}, {table_name.schema}")
    #     table_name = obj.name
    #     autogen_context.imports.add("from sqlalchemy.engine.reflection import Inspector")

    # table_creation_code = autogen_context.get_render_item(type_, obj, autogen_context)
    # if table_name.schema_name == "public" and 'CREATE TABLE' in table_creation_code:
    #     return f'''
    #         if not inspector.has_table("{table_name}", schema="{table_name.schema_name}"):
    #         {table_creation_code}
    #     '''

    # if_block = f"""
    #         inspector = Inspector.from_engine(op.get_bind())
    #         if '{table_name}' not in inspector.get_table_names(schema='public'):
    #             {table_creation_code}
    #         """
    # return if_block
    return False


def add_sequence_to_model(sequence, metadata):
    metadata.info.setdefault("sequences", set()).add(
        (sequence.schema, sequence.name)
    )


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        # dialect_opts={"paramstyle": "named"},
        include_schemas=include_schemas,
        version_table_schema='public',
        include_object=include_object,
        # Addition
        compare_type=True
    )

    with context.begin_transaction():
        # Mark as non-version-controlling
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """

    def process_revision_directives(
            _context: MigrationContext,
            revision: str | Iterable[str | None] | Iterable[str],
            directives: list[MigrationScript],
    ) -> None:
        assert config.cmd_opts is not None
        if getattr(config.cmd_opts, 'autogenerate', False):
            script = directives[0]
            assert script.upgrade_ops is not None
            if script.upgrade_ops.is_empty():
                directives[:] = []

        script = directives[0]

        # process both "def upgrade()", "def downgrade()"
        for directive in (script.upgrade_ops, script.downgrade_ops):
            # make a set of tables that are being dropped within
            # the migration function
            tables_dropped = set()
            for op in directive.ops:
                if isinstance(op, ops.DropTableOp):
                    tables_dropped.add((op.table_name, op.schema))

            # now rewrite the list of "ops" such that DropIndexOp
            # is removed for those tables.   Needs a recursive function.
            directive.ops = list(
                _filter_drop_indexes(directive.ops, tables_dropped)
            )
        # Added
        for op in script.upgrade_ops.ops:
            if hasattr(op, 'element') and hasattr(op.element, 'schema'):
                if op.element.schema is None:
                    op.element.schema = target_schema

        # upgrade_ops = script.upgrade_ops.ops

        # for directive in directives:
        #     if isinstance(directive, operations.ops.CreateTableOp):
        #         print(f"directive scheama = {directive.schema}")
        #         if directive.schema is None:
        #             directive.schema = get_target_schema()
        #         elif directive.schema == 'public':
        #             # Handle special cases for 'public' schema if needed
        #             directive.schema = get_target_schema()  # or leave it as is based on your logic
        # for op in upgrade_ops:
        #     print(op.info)

    def _filter_drop_indexes(directives, tables_dropped):
        # given a set of (tablename, schemaname) to be dropped, filter
        # out DropIndexOp from the list of directives and yield the result.

        for directive in directives:
            # ModifyTableOps is a container of ALTER TABLE types of
            # commands.  process those in place recursively.
            if isinstance(directive, ops.ModifyTableOps) and \
                    (directive.table_name, directive.schema) in tables_dropped:
                directive.ops = list(
                    _filter_drop_indexes(directive.ops, tables_dropped)
                )

                # if we emptied out the directives, then skip the
                # container altogether.
                if not directive.ops:
                    continue
            elif isinstance(directive, ops.DropIndexOp) and \
                    (directive.table_name, directive.schema) in tables_dropped:
                # we found a target DropIndexOp.   keep looping
                continue

            # otherwise if not filtered, yield out the directive
            yield directive

    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}
                           ),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,

    )
    if not database_exists(connectable.engine.url):
        create_database(connectable.engine.url)

    # target_schema = context.get_x_argument(as_dictionary=True).get("tenant", 'public')
    target_schema = get_target_schema()

    with connectable.connect() as connection:
        for value in ["citext", "ltree", "intarray", "hstore", "btree_gist"]:
            connection.execute(text(f"CREATE EXTENSION IF NOT EXISTS {value} WITH SCHEMA pg_catalog;"))

        # my_seq = Sequence("training_data_id_seq")
        # add_sequence_to_model(my_seq, target_metadata)

        if target_schema != 'public':
            connection.execute(text(f'CREATE SCHEMA IF NOT EXISTS {target_schema}'))
            connection.execute(text(f'set search_path to {target_schema}'))
            # in SQLAlchemy v2+ the search path change needs to be committed
            connection.commit()

            # make use of non-supported SQLAlchemy attribute to ensure
            # the dialect reflects tables in terms of the current tenant name
            connection.dialect.default_schema_name = target_schema
            # ctx = MigrationContext.configure(connection)
            # op = Operations(ctx)

        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            include_schemas=include_schemas,
            version_table_schema=target_schema,
            render_item=render_item,
            include_object=include_object,
            process_revision_directives=process_revision_directives,
            # include_name=include_name,
            # process_dependencies=True,
            # Additions
            # compare_type=True
        )

        with context.begin_transaction():
            context.run_migrations()

        # Debugging: Print the generated SQL to verify the constraints
        # for table in target_metadata.tables.values():
        #     if table.schema == target_schema:
        #         print(f"Table: {table.name}, Schema: {table.schema}")
        #         for constraint in table.constraints:
        #             if isinstance(constraint, UniqueConstraint):
        #                 print(f"Unique Constraint: {constraint}")


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
