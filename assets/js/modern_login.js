/**
 * Modern Login Page JavaScript
 * Handles password visibility toggle, form validation, and enhanced UX
 */

// Namespace for login functionality
window.modernLogin = {
    
    /**
     * Initialize login page functionality
     */
    init: function() {
        this.setupPasswordToggle();
        this.setupFormValidation();
        this.setupRememberCredentials();
        this.setupAccessibility();
        this.setupAnimations();
    },
    
    /**
     * Setup password visibility toggle
     */
    setupPasswordToggle: function() {
        const toggleButtons = document.querySelectorAll('.password-toggle');
        
        toggleButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const passwordInput = this.previousElementSibling;
                const icon = this.querySelector('i');
                
                if (passwordInput && passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    icon.className = 'fa fa-eye-slash';
                    this.setAttribute('aria-label', 'Hide password');
                } else if (passwordInput) {
                    passwordInput.type = 'password';
                    icon.className = 'fa fa-eye';
                    this.setAttribute('aria-label', 'Show password');
                }
            });
        });
    },
    
    /**
     * Setup form validation with real-time feedback
     */
    setupFormValidation: function() {
        const emailInput = document.getElementById('id-login-main');
        const passwordInput = document.getElementById('id-login-passwd-main');
        const loginButton = document.getElementById('id-button-login-main');
        
        if (emailInput) {
            emailInput.addEventListener('input', this.validateEmail.bind(this));
            emailInput.addEventListener('blur', this.validateEmail.bind(this));
        }
        
        if (passwordInput) {
            passwordInput.addEventListener('input', this.validatePassword.bind(this));
        }
        
        if (loginButton) {
            loginButton.addEventListener('click', this.handleLoginSubmit.bind(this));
        }
    },
    
    /**
     * Validate email input
     */
    validateEmail: function(event) {
        const input = event.target;
        const value = input.value.trim();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        this.clearFieldError(input);
        
        if (value && !emailRegex.test(value)) {
            this.showFieldError(input, 'Please enter a valid email address');
            return false;
        }
        
        if (value) {
            this.showFieldSuccess(input);
        }
        
        return true;
    },
    
    /**
     * Validate password input
     */
    validatePassword: function(event) {
        const input = event.target;
        const value = input.value;
        
        this.clearFieldError(input);
        
        if (value && value.length < 6) {
            this.showFieldError(input, 'Password must be at least 6 characters');
            return false;
        }
        
        if (value) {
            this.showFieldSuccess(input);
        }
        
        return true;
    },
    
    /**
     * Show field error
     */
    showFieldError: function(input, message) {
        const inputGroup = input.closest('.modern-input-group');
        if (!inputGroup) return;
        
        input.style.borderColor = 'var(--error-color)';
        
        let errorElement = inputGroup.querySelector('.field-error');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'field-error';
            errorElement.style.cssText = `
                color: var(--error-color);
                font-size: 0.75rem;
                margin-top: 0.25rem;
                display: flex;
                align-items: center;
                gap: 0.25rem;
            `;
            inputGroup.appendChild(errorElement);
        }
        
        errorElement.innerHTML = `<i class="fa fa-exclamation-circle"></i> ${message}`;
    },
    
    /**
     * Show field success
     */
    showFieldSuccess: function(input) {
        input.style.borderColor = 'var(--success-color)';
        this.clearFieldError(input);
    },
    
    /**
     * Clear field error
     */
    clearFieldError: function(input) {
        const inputGroup = input.closest('.modern-input-group');
        if (!inputGroup) return;
        
        input.style.borderColor = '';
        
        const errorElement = inputGroup.querySelector('.field-error');
        if (errorElement) {
            errorElement.remove();
        }
    },
    
    /**
     * Handle login form submission
     */
    handleLoginSubmit: function(event) {
        const emailInput = document.getElementById('id-login-main');
        const passwordInput = document.getElementById('id-login-passwd-main');
        const button = event.target;
        
        let isValid = true;
        
        // Validate email
        if (emailInput) {
            const emailEvent = { target: emailInput };
            if (!this.validateEmail(emailEvent)) {
                isValid = false;
            }
            
            if (!emailInput.value.trim()) {
                this.showFieldError(emailInput, 'Email is required');
                isValid = false;
            }
        }
        
        // Validate password
        if (passwordInput) {
            const passwordEvent = { target: passwordInput };
            if (!this.validatePassword(passwordEvent)) {
                isValid = false;
            }
            
            if (!passwordInput.value) {
                this.showFieldError(passwordInput, 'Password is required');
                isValid = false;
            }
        }
        
        if (!isValid) {
            event.preventDefault();
            return false;
        }
        
        // Show loading state
        this.showLoadingState(button);
        
        // Store credentials for remember functionality
        this.storeCredentials(emailInput?.value);
        
        return true;
    },
    
    /**
     * Show loading state on button
     */
    showLoadingState: function(button) {
        button.classList.add('loading');
        button.disabled = true;
        
        // Remove loading state after 5 seconds as fallback
        setTimeout(() => {
            button.classList.remove('loading');
            button.disabled = false;
        }, 5000);
    },
    
    /**
     * Setup remember credentials functionality
     */
    setupRememberCredentials: function() {
        const emailInput = document.getElementById('id-login-main');
        
        if (emailInput) {
            // Load saved email
            const savedEmail = localStorage.getItem('login_email');
            if (savedEmail) {
                emailInput.value = savedEmail;
            }
        }
    },
    
    /**
     * Store credentials in localStorage
     */
    storeCredentials: function(email) {
        if (email) {
            localStorage.setItem('login_email', email);
        }
    },
    
    /**
     * Setup accessibility features
     */
    setupAccessibility: function() {
        // Add proper ARIA labels
        const emailInput = document.getElementById('id-login-main');
        const passwordInput = document.getElementById('id-login-passwd-main');
        
        if (emailInput) {
            emailInput.setAttribute('aria-label', 'Email address');
            emailInput.setAttribute('autocomplete', 'email');
        }
        
        if (passwordInput) {
            passwordInput.setAttribute('aria-label', 'Password');
            passwordInput.setAttribute('autocomplete', 'current-password');
        }
        
        // Setup keyboard navigation
        this.setupKeyboardNavigation();
    },
    
    /**
     * Setup keyboard navigation
     */
    setupKeyboardNavigation: function() {
        const form = document.querySelector('.modern-login-form');
        if (!form) return;
        
        form.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                const loginButton = document.getElementById('id-button-login-main');
                if (loginButton && !loginButton.disabled) {
                    loginButton.click();
                }
            }
        });
    },
    
    /**
     * Setup entrance animations
     */
    setupAnimations: function() {
        // Add staggered animation to form elements
        const formElements = document.querySelectorAll('.modern-input-group, .modern-login-button');
        
        formElements.forEach((element, index) => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                element.style.transition = 'all 0.4s ease-out';
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }, 200 + (index * 100));
        });
    },
    
    /**
     * Show notification message
     */
    showNotification: function(message, type = 'info') {
        const container = document.getElementById('notifications-container');
        if (!container) return;
        
        const notification = document.createElement('div');
        notification.className = `modern-${type}-message`;
        notification.innerHTML = `
            <i class="fa fa-${type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            ${message}
        `;
        
        container.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.opacity = '0';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        }, 5000);
    }
};

// Initialize when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        if (document.body.classList.contains('login-page')) {
            modernLogin.init();
        }
    });
} else {
    if (document.body.classList.contains('login-page')) {
        modernLogin.init();
    }
}

// Re-initialize when navigating to login page
document.addEventListener('DOMContentLoaded', function() {
    // Watch for URL changes
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                if (document.body.classList.contains('login-page')) {
                    setTimeout(() => modernLogin.init(), 100);
                }
            }
        });
    });

    observer.observe(document.body, {
        attributes: true,
        attributeFilter: ['class']
    });
});

// Export for Dash clientside callbacks
window.dash_clientside = window.dash_clientside || {};
window.dash_clientside.modernLogin = {
    
    /**
     * Toggle password visibility
     */
    togglePassword: function(n_clicks, current_type) {
        if (!n_clicks) return current_type;
        
        return current_type === 'password' ? 'text' : 'password';
    },
    
    /**
     * Validate form on submit
     */
    validateForm: function(n_clicks, email, password) {
        if (!n_clicks) return [false, ''];
        
        const errors = [];
        
        if (!email || !email.trim()) {
            errors.push('Email is required');
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.trim())) {
            errors.push('Please enter a valid email address');
        }
        
        if (!password) {
            errors.push('Password is required');
        } else if (password.length < 6) {
            errors.push('Password must be at least 6 characters');
        }
        
        if (errors.length > 0) {
            return [false, errors.join('. ')];
        }
        
        return [true, ''];
    }
};
