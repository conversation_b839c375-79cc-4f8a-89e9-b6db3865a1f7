/* Modern Login Page Styles */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  /* Color Palette */
  --primary-color: #6366f1;
  --primary-hover: #5855eb;
  --primary-light: #e0e7ff;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --error-color: #ef4444;
  --warning-color: #f59e0b;
  
  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Login Page Body Styles */
body.login-page {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow-x: hidden;
}

/* Animated Background */
body.login-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  animation: backgroundMove 20s ease-in-out infinite;
  z-index: -1;
}

@keyframes backgroundMove {
  0%, 100% { transform: translateX(0) translateY(0); }
  25% { transform: translateX(-20px) translateY(-10px); }
  50% { transform: translateX(20px) translateY(10px); }
  75% { transform: translateX(-10px) translateY(20px); }
}

/* Modern Login Container */
.modern-login-container {
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-2xl);
  width: 100%;
  max-width: 400px;
  margin: var(--spacing-md);
  position: relative;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Login Header */
.modern-login-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.modern-login-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--gray-900);
  margin: 0 0 var(--spacing-sm) 0;
  letter-spacing: -0.025em;
}

.modern-login-subtitle {
  font-size: 0.875rem;
  color: var(--gray-500);
  margin: 0;
  font-weight: 400;
}

/* Form Styles */
.modern-login-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

/* Input Group */
.modern-input-group {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.modern-input-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: var(--spacing-xs);
}

/* Modern Input Field */
.modern-input-field {
  position: relative;
  display: flex;
  align-items: center;
}

.modern-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.75rem;
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-md);
  font-size: 1rem;
  font-weight: 400;
  color: var(--gray-900);
  background-color: var(--white);
  transition: all 0.2s ease-in-out;
  box-sizing: border-box;
}

.modern-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.modern-input::placeholder {
  color: var(--gray-400);
  font-weight: 400;
}

/* Input Icons */
.modern-input-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
  font-size: 1.125rem;
  z-index: 2;
  transition: color 0.2s ease-in-out;
}

.modern-input:focus + .modern-input-icon {
  color: var(--primary-color);
}

/* Password Toggle */
.password-toggle {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--gray-400);
  cursor: pointer;
  font-size: 1.125rem;
  padding: 0.25rem;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease-in-out;
  z-index: 2;
}

.password-toggle:hover {
  color: var(--gray-600);
  background-color: var(--gray-100);
}

/* Modern Button */
.modern-login-button {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  color: var(--white);
  border: none;
  border-radius: var(--radius-md);
  padding: 0.875rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
  text-transform: none;
  letter-spacing: 0.025em;
}

.modern-login-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.modern-login-button:active {
  transform: translateY(0);
}

.modern-login-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Loading State */
.modern-login-button.loading {
  color: transparent;
}

.modern-login-button.loading::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  top: 50%;
  left: 50%;
  margin-left: -10px;
  margin-top: -10px;
  border: 2px solid transparent;
  border-top-color: var(--white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error Messages */
.modern-error-message {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: var(--error-color);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  margin-top: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* Success Messages */
.modern-success-message {
  background-color: #f0fdf4;
  border: 1px solid #bbf7d0;
  color: var(--success-color);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  margin-top: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* Responsive Design */
@media (max-width: 640px) {
  .modern-login-container {
    margin: var(--spacing-sm);
    padding: var(--spacing-xl);
    max-width: none;
  }
  
  .modern-login-title {
    font-size: 1.5rem;
  }
  
  .modern-input {
    padding: 0.625rem 0.875rem 0.625rem 2.5rem;
  }
  
  .modern-input-icon {
    left: 0.625rem;
    font-size: 1rem;
  }
  
  .password-toggle {
    right: 0.625rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  body.login-page {
    padding: var(--spacing-sm);
  }
  
  .modern-login-container {
    padding: var(--spacing-lg);
  }
}

/* Animation for form appearance */
.modern-login-container {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus states for accessibility */
.modern-input:focus,
.modern-login-button:focus,
.password-toggle:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .modern-input {
    border-width: 2px;
  }
  
  .modern-login-button {
    border: 2px solid var(--primary-color);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  body.login-page::before {
    animation: none;
  }
}
