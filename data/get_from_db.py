from __future__ import annotations

import enum
from functools import wraps
from collections import defaultdict, namedtuple
import numpy as np
from sqlalchemy.exc import DataError
from flask import g
from sqlalchemy import (
    create_engine, func, select, or_, and_, case, literal_column, event, text, Numeric,
    Column, Integer, String, Date, cast, ARRAY, union
)

from sqlalchemy.dialects.postgresql import TEXT, insert, NUMERIC

from sqlalchemy.inspection import inspect
from sqlalchemy.orm import sessionmaker, aliased, joinedload, selectinload, Session
from sqlalchemy.sql.expression import ClauseElement, Executable, literal
from sqlalchemy.ext.compiler import compiles

from pykeepass import PyKeePass as pkp
from urllib.parse import quote

import pandas as pd
import psycopg2 as pg
import logging
from logging import LogRecord, Handler
from typing import Optional, Callable, ContextManager

# import dash_app
from custom_container import (
    AppContainer, DbConnectionURI, Database,
    MSSQLDatabase, KPDatabaseOpener, KeePassPasswordManager
)
from data.helper import SHORT_TIME_OUT, TIME_OUT, TimeCodeBlock
from data.custom_logger import MyLogger
# from custom_logger import MyLogger

# try:
#     from dash_app import cache
# except ModuleNotFoundError:
#     from ..dash_app import cache

from datetime import datetime, date
import os

import time
from dbmodels import (
    AllBoards, Teams, WorkLog,
    Issue, InitiativeAttribute, IssueClassification,
    RunDetails, Versions, PlatformVersion, Base, User, ChangeLog,
    GSJiraIssues, RequestTracker, NLPTrainingData
)
# from dbmodels.base import Base
# from dbmodels.allboards import AllBoards
# from dbmodels.teams import Teams
# from dbmodels.worklog import WorkLog
# from dbmodels.user import User, CacheyCIText
#
# from dbmodels.issue import Issue
# from dbmodels.initiativeattribute import InitiativeAttribute
# from dbmodels.versions import Versions
# from dbmodels.issueclassification import IssueClassification
# from dbmodels.rundetails import RunDetails
# from dbmodels.platform_version import PlatformVersion
from dbmodels.svnbranch import SVNBranch, SVNPackage, CCJiraBranchCheckIn, CCJiraPackageCheckIn

# from functools import singledispatch
from multipledispatch import dispatch
from packaging import version
from distutils.version import LooseVersion
import re
from dependency_injector.wiring import inject, Provide

from dbmodels.user import RunDetailsView

if __name__ == "__main__":
    from app import cache
else:
    from data.decorators import cache


# password_manager = MyKeePassContainer().password_manager
#
# uri_ro = DbConnectionURI(password_manager, read_only=True)
# uri_rw = DbConnectionURI(password_manager, read_only=False)

def with_schema_translate_map(schema_name_param: str) -> Callable:
    """
    A decorator that adds support for custom schema translation to a SQLAlchemy session.
    The decorated function must accept a SQLAlchemy session object as an argument.

    Usage:
    @with_schema_translate_map('plat')
    def my_function(session):
        # perform database operations using the session with the updated engine instance
        ...
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # execute the command on the engine and get the updated engine instance
            session = kwargs.get('session')
            if session is not None:
                schema_name = kwargs.get(schema_name_param)
                if schema_name is not None:
                    updated_engine = session.get_bind().execution_options(schema_translate_map={None: schema_name})
                    # update the session's bind attribute with the updated engine instance
                    session.bind = updated_engine

            # call the decorated function with the updated session object
            return func(*args, **kwargs)

        return wrapper

    return decorator



def invalidate_user_otp_cache_on_update(target, value, oldvalue, initiator):
    if 'otp_secret' in target.__dict__:
        # Get the primary key values for the updated row
        primary_key_values = [User.accountId]
        # Generate the cache key for the row
        cache_key = 'user_token_{}'.format(*primary_key_values)
        # Delete the cache entry for the row
        # cache.delete(cache_key)


event.listen(User.otp_secret, 'set', invalidate_user_otp_cache_on_update)

# Define event listener to set insert_event_occurred flag on insert
for model in [User, Versions, Teams]:
    @event.listens_for(model, 'after_delete')
    @event.listens_for(model, 'after_insert')
    def set_insert_event_occurred_flag_on_insert(mapper, connection, target):
        my_logger = MyLogger().get_logger()
        my_logger.debug(f"Insert event detected on {mapper.class_} Table")


def unless_user_insert():
    my_logger = MyLogger().get_logger()
    try:
        if hasattr(g, 'user_insert_event_occurred') and g.user_insert_event_occurred:
            my_logger.debug("Insert event detected on User Table")
            g.user_insert_event_occurred = False
            return True
    except RuntimeError:
        return False
    return False


def unless_versions_change():
    my_logger = MyLogger().get_logger()
    if hasattr(g, 'version_change_event_occurred') and g.version_change_event_occurred:
        my_logger.debug("Insert or delete event detected on Version Table")
        g.version_change_event_occurred = False
        return True
    return False


# def unless_team_change():
#     my_logger = MyLogger().get_logger()
#     if dash_app.session:
#         my_logger.debug("session is Active")
#     else:
#         my_logger.debug("session is not active")
#
#     if dash_app.session and hasattr(g, 'team_change_event_occurred') and g.team_change_event_occurred:
#         my_logger.debug("Insert or delete event detected on Teams Table")
#         g.team_change_event_occurred = False
#         return True
#     return False


# Source: https://github.com/sqlalchemy/sqlalchemy/wiki/Query-Plan-SQL-construct
class Explain(Executable, ClauseElement):
    inherit_cache = True
    cache_ok = False

    def __init__(self, stmt, analyze=False):
        self.statement = stmt
        self.analyze = analyze


@compiles(Explain, "postgresql")
def pg_explain(element, compiler, **kw):
    text = "EXPLAIN "
    if element.analyze:
        text += "ANALYZE "
    else:
        text += "(FORMAT JSON) "
    text += compiler.process(element.statement, **kw)

    return text


class ExplainPlanHandler(Handler):
    def __init__(self, explain=False):
        super().__init__()
        self.explain = explain

    def emit(self, record) -> None:
        if self.explain:
            explain_plan = text("EXPLAIN (FORMAT JSON) " + record.msg)
        else:
            query = text(record.msg)
            # stmt = query.execution_options(joinedload('children'))
            stmt = query.options(joinedload('children')).execution_options()

            explain_plan = Explain(stmt)
        result_ = record.db.execute(explain_plan).fetchone()
        print(result_)


class LogRecord(logging.LogRecord):
    db: Session
    _logger = None

    def __new__(cls, logname=__name__, use_formatter=True, *args, **kwargs):
        if cls._logger is None:
            cls._logger = super(LogRecord, cls).__new__(cls, *args, **kwargs)
            cls._logger = logging.getLogger(logname)
            cls._logger.setLevel(logging.DEBUG)
            cls._logger.propagate = True
            dirname = "./log"

            if not os.path.isdir(dirname):
                os.mkdir(dirname)

            cls._logger.addHandler(ExplainPlanHandler())

    def __init__(self, name, level, fn, lno, msg, args, exc_info, func=None, sinfo=None, db=None):
        self.db = db
        super().__init__(name, level, fn, lno, msg, args, exc_info, func, sinfo)

    def _log(self, level, msg, args, exc_info=None, extra=None, stack_info=False, **kwargs):
        # use your overridden LogRecord class
        if extra is not None:
            extra = {'db': extra.get('db')}
        else:
            extra = {'db': None}
        super()._log(level, msg, args, exc_info=exc_info, extra=extra, stack_info=stack_info, **kwargs)


# logger = logging.getLogger("explain_plan_logger")
# logger.setLevel(logging.DEBUG)
# handler = ExplainPlanHandler()
# logger.addHandler(handler)


# Source: https://gist.github.com/garaud/bda10fa55df5723e27da
def query_to_dict(rows):
    """From a Query.all(), turn this result to a pandas DataFrame
    Table creation and example data come from the official SQLAlchemy ORM
    tutorial at http://docs.sqlalchemy.org/en/latest/orm/tutorial.html
    CREDIT: `<https://gist.github.com/garaud/bda10fa55df5723e27da>`
    """
    result = defaultdict(list)
    for obj in rows:
        instance = inspect(obj)
        for key, x in instance.attrs.items():
            result[key].append(x.value)
    return result


def query_to_list(rset):
    """From a Query.all(), turn this result to a pandas DataFrame"""
    result = []
    instance = None
    for obj in rset:
        instance = inspect(obj)
        items = instance.attrs.items()
        result.append([x.value for _, x in items])
    return instance.attrs.keys() if instance is not None else None, result


def start_session_factory(
        project: Optional[str] = None, read_only: bool = True) \
        -> Callable[..., ContextManager[Session]]:
    session_obj = Database(DbConnectionURI(...), schema_name=project)
    return session_obj.session()


def start_session(schema_name: Optional[str] = 'plat', read_only: str = True) -> sessionmaker():
    """
    Function to establish the connection with postgresql DB

    Args:
        read_only: no right operation involved
        schema_name: Corresponds to postgress schema name
    Returns:
        sessionmaker object
    """
    # schema = f"{schema_name},public"
    home_path = os.getenv('HOME', 'c:/vishal/KeyPass')

    ref = pkp(
        filename=os.getenv('DATABASE_PATH'),
        keyfile=os.getenv('MASTER_KEYFILE')
    )

    if os.name == "nt" or os.uname().nodename == "dashboard":
        entry = ref.find_entries(title='lilly', first=True)
        user = entry.username
        pwd = entry.password

        # Define the connection value
        conn_str = 'postgresql+psycopg2://{username}:{password}@localhost:5432/{database}'.format(
            username=user,
            password=quote(pwd),
            database='jira'
        )
    else:
        entry = ref.find_entries(title='PG_DB', first=True)
        user = entry.username
        pwd = entry.password
        driver_name = os.getenv('PG_DRIVER_NAME', 'postgresql+psycopg2')
        # my_logger.info(f"Env value: {os.getenv('PG_DRIVER_NAME', None)}")
        server_name = os.getenv('PG_SERVER_NAME', '*************')
        if read_only:
            server_port = os.getenv('PG_SERVER_PORT_RO', '5001')
        else:
            server_port = os.getenv('PG_SERVER_PORT_RW', '5000')
        server_db = os.getenv('PG_DB_NAME', 'jira')
        conn_str = '{driver_name}://{username}:{password}@{host}:{port}/{database}'.format(
            driver_name=driver_name,
            username=user,
            password=quote(pwd),
            host=server_name,
            port=server_port,
            database=server_db
        )

    # an Engine, which the Session will use for connection
    # resources

    engine = create_engine(conn_str, query_cache_size=500, echo=False).execution_options(
        schema_translate_map={None: schema_name})

    db_session = sessionmaker()
    db_session.configure(bind=engine)
    # No tables getting created here
    # Hence, keep it commented
    # Base.metadata.create_all(engine)

    # we can now construct a Session() without needing to pass the
    # engine each time
    return db_session()


def upsert(
        session: Session, model, rows: pd.DataFrame, primary_key: str,
        no_update_cols: tuple = (), on_conflict_update=True
):
    # Source: https://stackoverflow.com/questions/7165998/how-to-do-an-upsert-with-sqlalchemy/44395983#44395983
    table = model.__table__

    stmt = insert(table).values(rows.to_dict(orient='records'))

    update_cols = [
        c.name for c in table.c
        if c not in list(table.primary_key.columns) and c.name not in no_update_cols
    ]

    if on_conflict_update:
        on_conflict_stmt = stmt.on_conflict_do_update(
            index_elements=table.primary_key.columns,
            set_={k: getattr(stmt.excluded, k) for k in update_cols},
            index_where=(getattr(model, primary_key) == getattr(stmt.excluded, primary_key))
        )
    else:
        on_conflict_stmt = stmt.on_conflict_do_nothing(index_elements=table.primary_key.columns)
    session.execute(on_conflict_stmt)


def get_board_id(db_session: Session, project: str) -> list[dict]:
    """
    Queries AllBoards table and returns board id and its corresponding name
    Args:
        db_session:
        project: str
        Corresponds to postgress schema name. This ties with JIRA project key as well except for public schema

    Returns: list[dict]
        board id and corresponding name as list of dictionary
    """

    result = db_session.query(AllBoards).filter(
        (AllBoards.projectKey.in_([project.upper()])) & (AllBoards.type.in_(['scrum']))
    ).all()

    return [{'label': i.name, 'value': i.id} for i in result]


def get_epic_linked_issues(project: str, issue_list: list) -> pd.DataFrame:
    session = start_session(project)

    result = session.query(Issue).filter(
        (Issue.parent_key.in_(issue_list))
    ).all()
    return pd.DataFrame(query_to_dict(result))


@cache.memoize(timeout=TIME_OUT)
def get_user_counts(pg_session: Session):
    return pg_session.query(
        func.count().filter(User.active == 'true', User.accountType == 'atlassian').label("ActiveUser"),
        func.count().filter(User.active == 'false', User.accountType == 'atlassian').label("InactiveUser"),
        func.count().filter(User.active == 'true', User.accountType == 'app').label("ActiveApp"),
        func.count().filter(User.active == 'false', User.accountType == 'app').label("InactiveApp"),
        func.count().filter(User.active == 'true', User.accountType == 'customer').label("ActiveSD"),
        func.count().filter(User.active == 'false', User.accountType == 'customer').label("InactiveSD")
    ).one()


def get_run_details_mv(pg_session: Session):
    result = pg_session.query(RunDetailsView).all()
    return result


def get_users(pg_session: Session, email_address: Optional[str] = None):
    """
    Returns all users is specific email id is not given
    Args:
        pg_session:
        email_address:

    Returns:

    """
    query = pg_session.query(User)
    if email_address:
        query = query.filter(User.emailAddress == email_address)
    return query.all()


def get_user_detail(pg_session) -> pd.DataFrame:
    """
    Query the user table to get all users
    Returns: table rows as a dataframe
    """

    # x = my_logger.get_logger()
    # x.info("Check if this logs data")

    query = select(
        User.accountType, User.active, User.accountId, User.displayName, User.emailAddress, User.otp_secret
    ).select_from(User)

    # with start_session('public') as session:
    result = pg_session.execute(query).all()
    if os.getenv("QUERY_PLAN", None):
        my_logger = MyLogger()
        query_plan = pg_session.execute(Explain(query, analyze=True)).all()
        # need to check how to convert while dumping in my logger
        # json_data = [dict(row) for row in query_plan]
        my_logger.log_explain_plan(f'{query_plan}')

        # my_logger.debug('\n'.join(map(str, query_plan)))

    df = pd.DataFrame(result, columns=[
        "Account Type", "Status", "accountId", "displayName", "emailAddress",
        "otp_secret"
    ]
                      )
    df['Account Type'] = df['Account Type'].map({"dash_app": "Functional", "atlassian": "User"})

    return df


def get_single_user(email_address: str, pg_session: Session) -> list:
    """
    Function to return single row from User
    Args:
        email_address: email id being queries

    Returns: [sqlalchemy.engine.row.Row]

    """
    stmt = select(
        User.accountId, User.displayName, User.emailAddress, User.otp_secret
    ).select_from(User).filter(User.emailAddress == email_address)

    return pg_session.execute(stmt).all()


def get_user_otp_token(email_address: str):
    with start_session('public') as session:
        stmt = select(func.coalesce(User.otp_secret, '')).filter(User.emailAddress == email_address)
        result = session.execute(stmt).all()

    for row in result:
        value = ''.join(row)
        return value
    return 'NOT_FOUND'


def update_user_otp_token(email_address: str, otp_token: str, ):
    with start_session('public') as session:
        session.query(
            User
        ).filter(User.emailAddress == email_address).update(
            {
                User.otp_secret: otp_token,
                User.otp_secret_added_on: datetime.today()
            }
        )
        # user = session.query(User).filter(User.emailAddress == email_address).one()
        # user.otp_secret = otp_token
        # user.otp_secret_added_on = datetime.today()
        # Commit the changes to the database
        session.commit()


def get_run_details(pg_session):
    # with start_session(project) as session:
    # result = pg_session.query(RunDetails).all()
    # return pd.DataFrame(query_to_dict(result))
    return pg_session.query(RunDetails).all()


# @cache.memoize(SHORT_TIME_OUT, unless=unless_versions_change)
def get_versions(pg_session: Session):
    result = pg_session.query(Versions).all()
    return pd.DataFrame(query_to_dict(result))


def sort_alpa_numeric(names: list):
    """
    Source: https://stackoverflow.com/a/2669120/********
    Sort the alpha numeric list in numerical order
    Args:
        names: unsorted list of names

    Returns: sorted list
    """

    convert = lambda text: int(text) if text.isdigit() else 0
    alphanum_key = lambda key: [convert(c) for c in re.split(r'([ _.])', key)]
    return sorted(names, key=alphanum_key, reverse=True)


def get_branch_details(session) -> list:
    stmt = select(
        SVNBranch.name
    ).select_from(SVNBranch)

    res = session.execute(stmt).all()
    res = [branch for branch_name in res for branch in branch_name]
    return sort_alpa_numeric(res)


def get_package_details(session) -> list:
    stmt = select(
        SVNPackage.name
    ).select_from(SVNPackage) \
        .filter(SVNPackage.name.like('Plat%')) \
        .filter(SVNPackage.name.not_like('%_bad'))

    res = session.execute(stmt).all()
    res = [package for package_name in res for package in package_name]
    return sort_alpa_numeric(res)


def get_branch_package_summary(
        branch_name: list, package_name: list, project: str, pg_session: Session
) -> (pd.DataFrame, pd.DataFrame):
    with pg_session as session:
        stmt = select(
            CCJiraPackageCheckIn.package_name.label("Package Name"),
            func.max(CCJiraPackageCheckIn.app_index_change).label("Primary Index Change"),
            func.sum(CCJiraPackageCheckIn.app_index_change).label("#Primary Index Change"),
            func.max(CCJiraPackageCheckIn.report_index_change).label("Replication Index Change"),
            func.sum(CCJiraPackageCheckIn.report_index_change).label("#Replication Index Change"),
            func.max(CCJiraPackageCheckIn.primary_schema_change).label("Primary Schema Change"),
            func.sum(CCJiraPackageCheckIn.primary_schema_change).label("#Primary Schema Change"),
            func.max(CCJiraPackageCheckIn.primary_sql_change).label("Primary SQL Change"),
            func.sum(CCJiraPackageCheckIn.primary_sql_change).label("#Primary SQL Change"),
            func.max(CCJiraPackageCheckIn.conversion_script).label("Conversion Script"),
            func.sum(CCJiraPackageCheckIn.conversion_script).label("#Conversion Script"),
            func.max(CCJiraPackageCheckIn.control_parameters).label("Control Parameter Change"),
            func.sum(CCJiraPackageCheckIn.control_parameters).label("#Control Parameter Change"),
        ).select_from(CCJiraPackageCheckIn) \
            .filter(CCJiraPackageCheckIn.package_name.in_(package_name)) \
            .group_by(CCJiraPackageCheckIn.package_name)
        result = session.execute(stmt).all()
        df1 = pd.DataFrame(result)

        stmt = select(
            CCJiraBranchCheckIn.branch_name.label("Branch Name"),
            func.max(CCJiraBranchCheckIn.app_index_change).label("Primary Index Change"),
            func.max(CCJiraBranchCheckIn.report_index_change).label("Replication Index Change"),
            func.max(CCJiraBranchCheckIn.primary_schema_change).label("Primary Schema Change"),
            func.max(CCJiraBranchCheckIn.primary_sql_change).label("Primary SQL Change"),
            func.max(CCJiraBranchCheckIn.conversion_script).label("Conversion Script"),
            func.max(CCJiraBranchCheckIn.control_parameters).label("Control Parameter Change"),
            func.sum(CCJiraBranchCheckIn.app_index_change).label("#Primary Index Change"),
            func.sum(CCJiraBranchCheckIn.report_index_change).label("#Replication Index Change"),
            func.sum(CCJiraBranchCheckIn.primary_schema_change).label("#Primary Schema Change"),
            func.sum(CCJiraBranchCheckIn.primary_sql_change).label("#Primary SQL Change"),
            func.sum(CCJiraBranchCheckIn.conversion_script).label("#Conversion Script"),
            func.sum(CCJiraBranchCheckIn.control_parameters).label("#Control Parameter Change"),
        ).select_from(CCJiraBranchCheckIn) \
            .filter(CCJiraBranchCheckIn.branch_name.in_(branch_name)) \
            .group_by(CCJiraBranchCheckIn.branch_name)
        result = session.execute(stmt).all()
        df2 = pd.DataFrame(result)

        for col in [
            'Primary Index Change', 'Replication Index Change',
            'Primary Schema Change', 'Primary SQL Change',
            'Conversion Script', 'Control Parameter Change'
        ]:
            if df1.shape[0] > 0:
                df1[col] = df1[col].map({1: 'Yes', 0: 'No'})
                # df1.replace([1, 0], ['Yes', 'No'], inplace=True)
            if df2.shape[0] > 0:
                df2[col] = df2[col].map({1: 'Yes', 0: 'No'})
        return df1, df2


def get_platform_version(pg_session: Session) -> list:
    """
    Get the platform versions from platform_version table in schema = project
    Args:
        pg_session:

    Returns:
        platform versions as a list

    """
    stmt = select(
        PlatformVersion.platform_version
    ).select_from(PlatformVersion)

    result = pg_session.execute(stmt).all()

    result_list = sorted(
        # Convert list of tuples to list
        [item for t in result for item in t],
        # Sort the resulting list in descending order
        key=lambda x: list(map(int, x.split('.'))),
        reverse=True
    )
    return result_list


def get_platform_description(pf_version: list, project: str, pg_session: Session):
    with pg_session as session:
        stmt = select(
            PlatformVersion.platform_version,
            func.unnest(PlatformVersion.description)
        ).select_from(PlatformVersion) \
            .filter(PlatformVersion.platform_version.in_(pf_version))
        result = session.execute(stmt).all()

        result = sorted(
            result,
            # Sort the resulting list in descending order
            key=lambda x: list(map(int, x[0].split('.'))),
            reverse=True
        )

    return pd.DataFrame(result, columns=['Platform Version', 'description'])


def get_sql_object_bugs(branch_name: list, key: str, project: str) -> pd.Series | pd.DataFrame:
    clause_dict = {
        'app_sql': [CCJiraBranchCheckIn.primary_sql_change == 1],
        'app_index': [CCJiraBranchCheckIn.app_index_change == 1],
        'rd_index': [CCJiraBranchCheckIn.report_index_change == 1],
        'app_schema': [CCJiraBranchCheckIn.primary_schema_change == 1],
    }
    rename_col_dict = {
        'app_sql': 'SQL Object Names',
        'app_index': 'App Indexes',
        'rd_index': 'Reporting Indexes',
        'app_schema': 'Alter Tables'
    }
    with start_session(project) as session:
        stmt = select(
            CCJiraBranchCheckIn.cc_jira,
            func.regexp_replace(CCJiraBranchCheckIn.filename, '.+/', '').label('SQL Object Names')
        ) \
            .select_from(CCJiraBranchCheckIn, Issue) \
            .filter(Issue.key == CCJiraBranchCheckIn.cc_jira) \
            .filter(Issue.issuetype.in_(['Bug', 'Production Defect', 'In-sprint Defects', 'UAT Defects'])) \
            .filter(*clause_dict[key]) \
            .filter(CCJiraBranchCheckIn.branch_name.in_(branch_name))
        result = session.execute(stmt).all()

    df = pd.DataFrame(result)
    df.fillna('', inplace=True)
    df.drop_duplicates(keep='first', inplace=True)

    df.rename(columns={'SQL Object Names': rename_col_dict[key]}, inplace=True)

    if df.shape[0] == 0:
        return pd.DataFrame(columns=['cc_jira', rename_col_dict[key]])

    # Convert list into value
    # df['ClientJira'] = df['ClientJira'].apply(lambda x: ','.join(map(str, x)))
    return df.groupby(['cc_jira'])[rename_col_dict[key]].apply(lambda x: ', '.join(x.astype(str)))


def get_sql_object(branch_name: list, key: str, project: str, pg_session: Session) -> pd.Series | pd.DataFrame:
    epic_issue = aliased(Issue)
    clause_dict = {
        'app_sql': [CCJiraBranchCheckIn.primary_sql_change == 1],
        'app_index': [CCJiraBranchCheckIn.app_index_change == 1],
        'rd_index': [CCJiraBranchCheckIn.report_index_change == 1],
        'app_schema': [CCJiraBranchCheckIn.primary_schema_change == 1],
    }
    rename_col_dict = {
        'app_sql': 'SQL Object Names',
        'app_index': 'App Indexes',
        'rd_index': 'Reporting Indexes',
        'app_schema': 'Alter Tables'
    }
    # if key == 'app_sql':
    #     clause = [CCJiraCheckin.primary_sql_change == 1]
    #
    with pg_session as session:
        stmt = select(
            IssueClassification.epic_key,
            # epic_issue.ClientJira,
            func.split_part(
                CCJiraBranchCheckIn.filename, '/',
                1 + func.char_length(CCJiraBranchCheckIn.filename) - func.char_length(
                    func.replace(CCJiraBranchCheckIn.filename, '/', ''))
            ).label('SQL Object Names')
        ).select_from(CCJiraBranchCheckIn, IssueClassification, Issue, epic_issue) \
            .filter(Issue.issuetype.not_in(['Bug', 'Production Defect', 'In-sprint Defects', 'UAT Defects'])) \
            .filter(CCJiraBranchCheckIn.cc_jira == Issue.key) \
            .filter(Issue.id == IssueClassification.id) \
            .filter(CCJiraBranchCheckIn.branch_name.in_(branch_name)) \
            .filter(*clause_dict[key]) \
            .filter(epic_issue.key == IssueClassification.epic_key)
        result = session.execute(stmt).all()
    df = pd.DataFrame(result)
    df.fillna('', inplace=True)
    df.drop_duplicates(keep='first', inplace=True)

    df.rename(columns={'SQL Object Names': rename_col_dict[key]}, inplace=True)

    if df.shape[0] == 0:
        return pd.DataFrame(columns=['epic_key', rename_col_dict[key]])

    # Convert list into value
    # df['ClientJira'] = df['ClientJira'].apply(lambda x: ','.join(map(str, x)))
    return df.groupby(['epic_key'])[rename_col_dict[key]].apply(lambda x: ', '.join(x.astype(str)))


def get_version_name(version_id: list, project: str):
    session = start_session(project)
    query = select(
        Versions.name
    ).select_from(Versions).filter(Versions.id.in_(version_id))

    result = session.execute(query).all()
    df = pd.DataFrame(result, columns=['name'])
    return df


#  unless=lambda: some_condition
@cache.memoize(timeout=SHORT_TIME_OUT)
def get_team_names(pg_session) -> list:
    query = select(
        func.distinct(Teams.team_name)
    ).select_from(Teams)

    res = pg_session.execute(query).all()
    return res


def get_team_members(team_name: str, project: str, pg_session: Session) -> pd.DataFrame:
    query = select(
        Teams.accountId,
        User.displayName
    ).select_from(Teams).join(User, User.accountId == Teams.accountId) \
        .filter(Teams.team_name == team_name) \
        .filter(Teams.endDate > datetime.today())

    rows = pg_session.execute(query).all()

    return pd.DataFrame(rows)


def get_issue_counts(project: str):
    session = start_session(project)

    query = select(func.unnest(Issue.sprintid), func.unnest(Issue.components),
                   Issue.issuetype, Issue.statusCategory,
                   # func.replace(func.ltree2text(func.subpath(Issue.path, 0, 1)), '_', '-'),
                   IssueClassification.initiative_key,
                   func.count(),
                   func.sum(Issue.aggregatetimeoriginalestimate), func.sum(Issue.storypoints)) \
        .select_from(Issue, IssueClassification).filter(Issue.key == IssueClassification.key) \
        .where(Issue.issuetype.in_(['Story', 'Task', 'Bug', 'In-sprint Defects', 'UAT Defect', 'Production Defect'])) \
        .group_by(Issue.sprintid, Issue.components, Issue.issuetype, Issue.statusCategory,
                  IssueClassification.initiative_key
                  )
    # print(session.execute(query).fetchall())
    result = session.execute(query).all()
    return pd.DataFrame(result,
                        columns=['sprintid', 'components', 'issuetype', 'statuscategory', 'initiative', 'count',
                                 "originalestimate",
                                 "storypoints"])


@dispatch(str, str, Session)
def get_worklog_details(start_date: str, end_date: str, pg_session: Session) -> pd.DataFrame:
    # issue_b = aliased(Issue)
    # issue_c = aliased(Issue)

    # clause = [Issue.id.any(issue_id) for issue_id in (func.cast(func.string_to_array(func.ltree2text(Issue.path_id), '.'), ARRAY(Integer)))]
    # issue_path = session.query(
    #     func.unnest(func.cast(func.string_to_array(func.ltree2text(Issue.path_id), '.'), ARRAY(Integer)))
    # )
    # # select_from(issue_c).join(issue_c.id == Issue.id).
    # assignee_name = session.query(User.displayName).filter(WorkLog.author == User.accountId)
    # team_name = session.query(Teams.team_name).filter(Teams.accountId == WorkLog.author) \
    #     .filter((WorkLog.started < Teams.endDate) | (Teams.endDate == None)) \
    #     .filter((WorkLog.started > Teams.startDate) | (Teams.startDate == None))
    #
    # epic_not_working = session.query(issue_b.key).filter(
    #     issue_b.id.in_(
    #         issue_path
    #     )
    # ).filter(issue_b.issuetype == 'Epic').subquery()
    #
    # initiative = session.query(issue_c.key).select_from(issue_c).filter(
    #     issue_c.id == (func.cast(func.string_to_array(func.ltree2text(Issue.path_id), '.'), ARRAY(Integer))).any_()
    # ).filter(issue_c.issuetype.in_(['Initiative', 'Initiative ']))
    #
    # epic = session.query(issue_c.key).select_from(issue_c).filter(
    #     issue_c.id == (func.cast(func.string_to_array(func.ltree2text(Issue.path_id), '.'), ARRAY(Integer))).any_()
    # ).filter(issue_c.issuetype.in_(['Epic']))
    # standard_issue = session.query(issue_c.key).select_from(issue_c).filter(
    #     issue_c.id == (func.cast(func.string_to_array(func.ltree2text(Issue.path_id), '.'), ARRAY(Integer))).any_()
    # ).filter(~issue_c.issuetype.in_(['Epic', 'Initiative', 'Initiative '])).filter(issue_c.isSubTask == False)

    query = select(
        Issue.key, Issue.issuetype, User.displayName, Issue.team_name,
        func.timezone('UTC', WorkLog.created).label('created'),
        func.timezone('UTC', WorkLog.updated).label('updated'),
        func.timezone('UTC', WorkLog.started).label('started'),
        (WorkLog.timeSpentSeconds / 3600),
        # func.replace(func.ltree2text(func.subpath(Issue.path, 0, 1)), '_', '-'),
        IssueClassification.initiative_key.label("initiative"),
        IssueClassification.epic_key.label("epic"),
        IssueClassification.standard_key.label("standard_issue"),
        # func.replace(func.ltree2text(func.subpath(Issue.path, 2, 1)), '_', '-'),
        WorkLog.id,
        Issue.issue_hierarchy_level, func.nlevel(Issue.path)
    ).select_from(WorkLog, Issue, IssueClassification).outerjoin(User, User.accountId == Issue.assignee) \
        .filter((WorkLog.started > start_date) & (WorkLog.started < end_date)) \
        .filter(Issue.id == WorkLog.issue_id).filter(Issue.id == IssueClassification.id)

    try:
        result = pg_session.execute(query).all()
    except pg.ProgrammingError as e:
        # logger.error("Programming error", stack_info=e)
        print(f'parameter passed: {start_date}, {end_date}')
        result = None
    except DataError as e:
        # logger.error("DataError", stack_info=e)
        print(e)
        result = None

    return pd.DataFrame(result, columns=[
        "key", "issuetype", "Name", "Team Name", "created", "updated", "started", "Time Spent (h)",
        "initiative", "epic", "standardissuetype", "wkid", "hierachy", "level"
    ])


@dispatch(str, datetime)
def get_worklog_details(prjkey: str, date_started: datetime = datetime(2022, 1, 1, 0, 0, 0)):
    session = start_session(prjkey)

    sprint_generator = select(

    )


def get_subtask_issue_description_stats(prjkey: str, start_date: datetime.date,
                                        end_date: datetime.date) -> pd.DataFrame:
    session = start_session(prjkey)
    query = select(
        func.date(func.timezone('UTC', Issue.created)),
        Issue.team_name, Issue.qc_check, func.count()
    ).select_from(Issue) \
        .filter(Issue.isSubTask.is_(True)) \
        .filter(Issue.issuetype.in_(['Design', 'Coding', 'Unit Test'])) \
        .filter(Issue.qc_check.is_not(None)) \
        .filter(Issue.team_name.is_not(None)) \
        .filter(func.date(func.timezone('UTC', Issue.created)).between(start_date, end_date)) \
        .filter(~Issue.status.in_(['Cancelled'])) \
        .group_by(func.date(func.timezone('UTC', Issue.created)), Issue.team_name, Issue.qc_check)

    result = session.execute(query).all()
    df = pd.DataFrame(result, columns=['created', 'Team', 'Quality', 'Count'])
    df = df[df['Team'].isin(['PLAT_REWARDS', 'PLAT_ACH', 'PLAT_API', 'PLAT_NOTIFICATION'])]
    return df


def get_subtask_issue_description(
        prjkey: str, start_date: datetime.date, end_date: datetime.date,
        pg_session: Session
) -> pd.DataFrame:
    query1 = select(
        Issue.key, Issue.issuetype,
        Issue.summary, Issue.description_markdown,
        func.date(Issue.created), User.displayName.label("assignee"), Issue.team_name, Issue.assignee,
        Issue.timeoriginalestimate, Issue.qc_check
    ).select_from(Issue).join(User, User.accountId == Issue.assignee, isouter=True) \
        .join(Teams, Teams.accountId == Issue.assignee, isouter=True) \
        .filter(Issue.issuetype.in_(['Design', 'Coding', 'Unit Test'])) \
        .filter(or_(Issue.qc_check == 'Pending', Issue.qc_check == 'Failed', Issue.qc_check.is_(None))) \
        .filter(~Issue.status.in_(['Cancelled'])) \
        .filter(func.date(func.timezone('UTC', Issue.created)).between(start_date, end_date))

    query2 = select(
        Issue.key, Issue.issuetype, Issue.summary, Issue.description_markdown,
        func.date(WorkLog.created), User.displayName, Teams.team_name, WorkLog.author,
        Issue.timeoriginalestimate, Issue.qc_check
    ).select_from(Issue, WorkLog).join(User, WorkLog.author == User.accountId) \
        .join(Teams, Teams.accountId == WorkLog.author, isouter=True) \
        .filter(Issue.id == WorkLog.issue_id) \
        .filter(func.date(func.timezone('UTC', WorkLog.created)).between(start_date, end_date)) \
        .filter(Issue.issuetype.in_(['Design', 'Coding', 'Unit Test'])) \
        .filter(or_(Issue.qc_check == 'Pending', Issue.qc_check == 'Failed', Issue.qc_check.is_(None))) \
        .filter(~Issue.status.in_(['Cancelled']))

    query = union(query1, query2)

    result = pg_session.execute(query).all()

    df = pd.DataFrame(
        result, columns=
        [
            "key", "issuetype", "summary", "description", "created", "assignee", "team",
            "accountId", "Effort Estimate", "Quality Check"
        ]
    )

    # Remove duplicate keys
    df.drop_duplicates(subset="key", keep='first', inplace=True)
    return df


@dispatch(str)
def get_worklog_details(prjkey: str):
    session = start_session(prjkey)
    epic = aliased(Issue)

    query = select(
        Issue.assignee_name, WorkLog.timeSpentSeconds / 3600,
        # InitiativeAttribute.attr['project'],
        InitiativeAttribute.project,
        func.subpath(Issue.path_id, 0, 1),
        func.replace(func.ltree2text(func.subpath(Issue.path, 0, 1)), '_', '-'),
        Issue.key, Issue.issuetype, Issue.isSubTask,
        epic.key, func.array_to_string(epic.ClientJira, ',', '*'),
        func.date_trunc('month', WorkLog.started).cast(Date),
        WorkLog.team_name,
        case(
            (Issue.issuetype.in_(['Bug', 'In-sprint Defects', 'Production Defect', 'UAT Defect']), 'Defects'),
            (InitiativeAttribute.project == 'Test Defects', 'Defects'),
            (InitiativeAttribute.project.in_(['Cookie', 'Jazz']), 'Enhancement'),
            (InitiativeAttribute.project.in_(['CC India']), 'Corporate'),
            (InitiativeAttribute.project.in_(['Jazz - Conv']), 'Conversion'),
            (InitiativeAttribute.project.in_(['Plat MS']), 'Managed Services'),
            (InitiativeAttribute.project.in_(['Plat PS']), 'Prod Support'),
            (InitiativeAttribute.project.in_(['Plat Audit']), 'Audit'),
            (InitiativeAttribute.project.in_(['Plat NBL']), 'Non Billable'),
            else_="Unclassified"
        )
    ).select_from(WorkLog, Issue, InitiativeAttribute, epic) \
        .filter(WorkLog.issue_id == Issue.id) \
        .filter(cast(InitiativeAttribute.initiative_id, String) == func.ltree2text(func.subpath(Issue.path_id, 0, 1))) \
        .filter(func.ltree2text(func.subpath(Issue.path_id, 1, 1)) == cast(epic.id, String))

    result = session.execute(query).all()
    return pd.DataFrame(result, columns=[
        'assignee', "timespent", "project", "initiative_id", "initiative_key", "key", "issuetype", "isSubTask",
        "epic_key", "epic_client_jira", "started_month", "team_name", "type"
    ])


def get_worklog_details_daterange(
        start_date: str, end_date: str, pg_session: Session
) -> pd.DataFrame:
    stmt = select(
        WorkLog.started.cast(Date).label("started"),
        func.date_trunc('week', WorkLog.started).cast(Date).label("week_start"),
        func.date_trunc('month', WorkLog.started).cast(Date).label("month_start"),
        InitiativeAttribute.project,
        InitiativeAttribute.release,
        IssueClassification.epic_key,
        Issue.key, Issue.issuetype, Issue.summary,
        WorkLog.author,
        User.displayName.label("team_member"),
        Teams.team_name,
        func.round(func.sum(WorkLog.timeSpentSeconds / 3600.0).cast(NUMERIC), 1).label("effort_spent")
    ).select_from(WorkLog) \
        .join(Issue, Issue.key == WorkLog.issue_key).join(User, User.accountId == WorkLog.author) \
        .join(IssueClassification, IssueClassification.key == Issue.key) \
        .join(InitiativeAttribute, InitiativeAttribute.initiative_id == IssueClassification.initiative_id) \
        .outerjoin(
        Teams,
        and_(
            Teams.accountId == WorkLog.author, WorkLog.started.between(Teams.startDate, Teams.endDate)
        ),
    ) \
        .filter(WorkLog.started.cast(Date).between(start_date, end_date)) \
        .group_by(
        WorkLog.started,
        func.date_trunc('week', WorkLog.started).cast(Date),
        func.date_trunc('month', WorkLog.started).cast(Date),
        InitiativeAttribute.project,
        InitiativeAttribute.release,
        IssueClassification.epic_key,
        Issue.key, Issue.issuetype, Issue.summary, User.displayName, Teams.team_name, WorkLog.author
    )

    rows = pg_session.execute(stmt).fetchall()
    df = pd.DataFrame(rows)
    df['started'] = pd.to_datetime(df['started']).dt.date
    df['week_start'] = pd.to_datetime(df['week_start']).dt.date
    df['month_start'] = pd.to_datetime(df['month_start']).dt.date
    df['effort_spent'] = pd.to_numeric(df['effort_spent'], errors='coerce')

    return df


def get_fixversion_details(fixversion: list, project: str):
    session = start_session(project)
    user_assignee = aliased(User)
    reporter_assginee = aliased(User)
    assignee_name = session.query(User.displayName).filter(Issue.assignee == User.accountId)
    reporter_name = session.query(User.displayName).filter(Issue.assignee == User.accountId)
    clause = [Issue.fixVersions.any(release) for release in fixversion]
    query = select(Issue.key, Issue.issuetype, Issue.isSubTask, Issue.statusCategory, Issue.resolutiondate,
                   Issue.components,
                   Issue.fixVersions, Issue.versions,
                   user_assignee.displayName.label('assignee'), reporter_assginee.displayName.label('reporter'),
                   Issue.created, Issue.updated,
                   Issue.sprint, Issue.Team, Issue.aggregatetimeoriginalestimate, Issue.aggregatetimespent,
                   Issue.aggregateprogress_percent
                   ). \
        select_from(Issue) \
        .outerjoin(user_assignee, user_assignee.accountId == Issue.assignee) \
        .join(reporter_assginee, reporter_assginee.accountId == Issue.reporter) \
        .filter(or_(*clause))
    # select_from(Issue.__table__).where(fixversion == Issue.fixVersions.any_())
    result = session.execute(query).all()
    return pd.DataFrame(result,
                        columns=['key', 'issuetype', 'issubtask', 'statuscategory', 'resolutiondate', 'components',
                                 'fixversions', 'versions', 'assignee', 'reporter', 'created', 'updated',
                                 'sprint', 'team',
                                 'aggregatetimeoriginalestimate', 'aggregatetimespent', 'aggregateprogress_percent'
                                 ])


def get_open_issues(pg_session: Session):
    # issue_c = aliased(Issue)
    #
    # initiative = pg_session.query(issue_c.key).select_from(issue_c).filter(
    #     issue_c.id == (func.cast(func.string_to_array(func.ltree2text(Issue.path_id), '.'), ARRAY(Integer))).any_()
    # ).filter(issue_c.issuetype.in_(['Initiative', 'Initiative ']))

    query = select(
        Issue.key, Issue.isSubTask, Issue.issuetype, Issue.parent_key, Issue.status,
        IssueClassification.initiative_key.label("initiative"),
        Issue.created.cast(Date), cast(Issue.updated, Date)
    ).select_from(Issue, IssueClassification) \
        .filter(Issue.resolution.is_(None)) \
        .filter(Issue.issuetype != 'Initiative ') \
        .filter(Issue.key == IssueClassification.key)
    result = pg_session.execute(query).all()
    return pd.DataFrame(result,
                        columns=['key', 'isSubTask', 'issuetype', 'parentkey', 'status', 'initiative', 'created',
                                 'updated'])


def get_release_details(fixversion: list, project: str) -> (pd.DataFrame, pd.DataFrame, pd.DataFrame):
    session = start_session(project)

    clause = [Issue.fixVersions.any(release) for release in fixversion]
    assignee_name = session.query(User.displayName).filter(Issue.assignee == User.accountId)
    reporter_name = session.query(User.displayName).filter(Issue.assignee == User.accountId)

    query = select(
        func.replace(func.ltree2text(func.subpath(Issue.path, 0, 1)), '_', '-'),
        Issue.key, Issue.parent_key, Issue.ClientJira, Issue.components, Issue.issuetype,
        Issue.statusCategory,
        Issue.status,
        Issue.reqfinalized, Issue.approvalstatus,
        Issue.baeffort, Issue.adeffort, Issue.rdeffort, Issue.qaeffort, Issue.contingency, Issue.totaleffort,
        # Issue.aggregatetimeoriginalestimate, Issue.aggregatetimeestimate, Issue.aggregatetimespent,
        # Issue.aggregateprogress_percent,
        # Issue.id
    ).select_from(Issue.__table__).filter(or_(*clause))

    result = session.execute(query).all()

    df = pd.DataFrame(result, columns=["Initiative", "IssueKey", "ParentKey", "Ticket#", "components", "Issue Type",
                                       "Status Category",
                                       "Status",
                                       "Req Finalized", "Approval Status", "totaleffort",
                                       "baeffort", "adeffort", "rdeffort", "qaeffort", "contingency",
                                       # "aggregatetimeoriginalestimate", "aggregatetimeestimate",
                                       # "aggregatetimespent", "aggregateprogress_percent", "IssueId"
                                       ])
    parent = df["IssueKey"].unique().tolist()
    df = df[~(df["ParentKey"].isin(parent))]

    query = select(
        Issue.key, Issue.issuetype, Issue.status, Issue.aggregateprogress_percent,
        Issue.aggregatetimeoriginalestimate, Issue.aggregatetimeestimate, Issue.aggregatetimespent,
        Issue.parent_key, Issue.id
    ).select_from(Issue.__table__).filter(Issue.parent_key.in_(parent))

    result = session.execute(query).all()

    df_st = pd.DataFrame(result, columns=["key", "issuetype", "status", "aggregateprogress_percent",
                                          "aggregatetimeoriginalestimate", "aggregatetimeestimate",
                                          "aggregatetimespent", "parentkey", "IssueId"
                                          ])
    standard_issue_key = df['IssueKey'].unique().tolist() + df_st["key"].unique().tolist()

    query = select(
        Issue.key, Issue.issuetype, Issue.status, assignee_name.label("assignee"),
        Issue.aggregateprogress_percent * 100,
        Issue.startdate, Issue.duedate,
        Issue.aggregatetimeoriginalestimate, Issue.aggregatetimeestimate, Issue.aggregatetimespent,
        Issue.parent_key
    ).select_from(Issue.__table__).filter(Issue.parent_key.in_(standard_issue_key)) \
        .filter(Issue.isSubTask == True)

    result = session.execute(query).all()

    df_subtask = pd.DataFrame(result, columns=[
        "key", "issuetype", "status", "assignee", "aggregateprogress_percent", "startdate", "duedate",
        "aggregatetimeoriginalestimate", "aggregatetimeestimate",
        "aggregatetimespent", "parentkey"
    ])

    return df, df_st, df_subtask


def get_matching_versions(input: list, is_released: bool, is_archived: bool, project: str) -> list:
    # filter(Versions.name.like(input)) \
    print(input)
    clause = [Versions.name.like(release) for release in input]
    query = select(
        Versions.id,
        Versions.name
    ).select_from(Versions) \
        .filter(or_(*clause)) \
        .filter(Versions.archived == is_archived) \
        .filter(Versions.released == is_released)
    session = start_session(project)
    result = session.execute(query).all()

    df = pd.DataFrame(result, columns=["id", "name"])
    return df['id'].tolist()


def get_sorted_initiative(project: str) -> list:
    session = start_session(project)
    query = select(
        Issue.key
    ).select_from(Issue).filter(Issue.issuetype.in_(['Initiative', 'Initiative '])).order_by(Issue.id)

    result = session.execute(query).all()
    df = pd.DataFrame(result, columns=['key'])
    return df['key'].values.tolist()


def calculate_aging(create_date):
    return np.busday_count(create_date, date.today())


def get_release_defect_status(version: list, pg_session: Session, selector: int = 0) -> pd.DataFrame:
    defect_issue_type = ['Bug', 'In-sprint Defects', 'UAT Defect', 'Production Defect', 'Observation']
    user_assignee = aliased(User)
    user_reporter = aliased(User)

    if selector == 1:
        clause = [Issue.fixVersions.any(release) for release in version]
        filter_version = Issue.fixVersions.op("<@")([version])
        filter_clause = Issue.fixVersions.op('&&')(version)
    else:
        clause = [Issue.versions.any(release) for release in version]
        filter_version = Issue.versions.op("<@")([version])
        filter_clause = Issue.versions.op('&&')(version)

    query = select(
        Issue.key, Issue.summary,
        Issue.status,
        case(
            (Issue.status.in_(['Open', 'In Progress']), 'With Dev'),
            (Issue.status.in_(['Fixed', 'Rejected', 'QA Testing']), 'With QA'),
            (Issue.status.in_(['On Hold', 'Future Testing']), 'Misc'),
            else_='Done'
        ).label("statusclass"),
        Issue.statusCategory,
        Issue.priority, Issue.severity,
        Issue.cvss_score.label("CVSS Score"),
        Issue.issuetype,
        Issue.reopen_count.label("reopen"), Issue.ClientJira.label("clientjira"),
        InitiativeAttribute.project,
        func.array_to_string(Issue.versions, ', ', '*').label("AffectsVersion"),
        func.array_to_string(Issue.fixVersions, ', ', '*').label("FixVersion"),
        user_assignee.displayName.label('assignee'),
        user_reporter.displayName.label('reporter'),
        cast(Issue.created, Date).label("created"),
        literal(0).label("aging"),
        Issue.Team,
        Issue.components,
        Issue.testcaseno.label("Test Case#"),

    ).select_from(Issue, IssueClassification, InitiativeAttribute) \
        .join(user_reporter, user_reporter.accountId == Issue.reporter) \
        .outerjoin(user_assignee, user_assignee.accountId == Issue.assignee) \
        .filter(Issue.id == IssueClassification.id) \
        .filter(InitiativeAttribute.initiative_id == IssueClassification.initiative_id) \
        .filter(Issue.issuetype.in_(defect_issue_type)) \
        .filter(filter_clause)

    rows = pg_session.execute(query).all()
    df = pd.DataFrame(rows)

    df['aging'] = df['created'].apply(calculate_aging)
    df.drop(columns=['created'], inplace=True)

    return df


@cache.cached(timeout=TIME_OUT, key_prefix='test_case_mapping')
def get_test_case_mapping(conn):
    query = conn.execute(
        text(
            f"""select 
                         test_id as TC_Number , Test_name, a.lutdescription as "Automationstatus"
                        from 
                        ISCProjectManagement.dbo.test_case WITH (NOLOCK)
                        left join 
                        ISCProjectManagement.dbo.lut a WITH (NOLOCK) on a.lutcode = Automationstatus and a.tablename = 'test_case' and fieldname = 'automationstatus'
                        where projectid in
                        ('1106082','1106084', '1106163', '1106223' )""")
    )
    rows = query.all()
    return rows


def get_release_status(fixversion: list, pg_session: Session) -> pd.DataFrame:
    issue_subtask = aliased(Issue)
    issue_epic = aliased(Issue)

    bug_list = ['Bug', 'In-sprint Defects', 'UAT Defect', 'Production Defect', 'Observation']

    subtask_filters = [
        issue_subtask.statusCategory == 'To Do',
        issue_subtask.statusCategory == 'In Progress',
        issue_subtask.statusCategory == 'Done'
    ]

    story_filters = [
        and_(Issue.issuetype.in_(['Story', 'Task']), Issue.statusCategory == 'To Do'),
        and_(Issue.issuetype.in_(['Story', 'Task']), Issue.statusCategory == 'In Progress'),
        and_(Issue.issuetype.in_(['Story', 'Task']), Issue.statusCategory == 'Done')
    ]

    bugs_filters = [
        and_(Issue.issuetype.in_(bug_list),
             Issue.statusCategory == 'To Do'),
        and_(Issue.issuetype.in_(bug_list),
             Issue.statusCategory == 'In Progress'),
        and_(Issue.issuetype.in_(bug_list),
             Issue.statusCategory == 'Done')
    ]

    others_filters = [
        and_(
            ~Issue.issuetype.in_(['Initiative ', 'Epic', 'Story', 'Task'] + bug_list),
            Issue.statusCategory == 'To Do',
            Issue.isSubTask == False
        ),
        and_(
            ~Issue.issuetype.in_(['Initiative ', 'Epic', 'Story', 'Task'] + bug_list),
            Issue.statusCategory == 'In Progress',
            Issue.isSubTask == False
        ),
        and_(
            ~Issue.issuetype.in_(
                ['Initiative', 'Epic', 'Story', 'Task'] + bug_list),
            Issue.statusCategory == 'Done',
            Issue.isSubTask == False
        )
    ]

    subtask_count = [
        func.coalesce(func.count().filter(subtask_filters[0]), 0).label("subtask_todo"),
        func.coalesce(func.count().filter(subtask_filters[1]), 0).label("subtask_wip"),
        func.coalesce(func.count().filter(subtask_filters[2]), 0).label("subtaskdone")
    ]

    story_count = [
        func.coalesce(func.count().filter(story_filters[0]), 0).label("story_todo"),
        func.coalesce(func.count().filter(story_filters[1]), 0).label("story_wip"),
        func.coalesce(func.count().filter(story_filters[2]), 0).label("storydone")
    ]

    bugs_count = [
        func.coalesce(func.count().filter(bugs_filters[0]), 0).label("defect_todo"),
        func.coalesce(func.count().filter(bugs_filters[1]), 0).label("defect_wip"),
        func.coalesce(func.count().filter(bugs_filters[2]), 0).label("defectsdone")
    ]

    others_count = [
        func.coalesce(func.count().filter(others_filters[0]), 0).label("other_todo"),
        func.coalesce(func.count().filter(others_filters[1]), 0).label("other_wip"),
        func.coalesce(func.count().filter(others_filters[2]), 0).label("otherdone")
    ]

    query1 = select(
        Issue.parent_key
    ).select_from(Issue.__table__) \
        .filter(Issue.fixVersions.op('&&')(fixversion)) \
        .filter(Issue.issuetype.in_(['Story', 'Task']))

    query2 = select(
        Issue.key
    ).select_from(Issue.__table__) \
        .filter(Issue.fixVersions.op('&&')(fixversion)) \
        .filter(Issue.issuetype == 'Epic')

    query = pg_session.query(
        InitiativeAttribute.project,
        InitiativeAttribute.initiative_key.label("initiative"),
        InitiativeAttribute.initiative_id,
        IssueClassification.epic_key.label("epic"),
        issue_epic.status,
        issue_epic.Team, issue_epic.ClientJira.label("clientjira"),
        issue_epic.components,
        *story_count,
        *bugs_count,
        *others_count,
        *subtask_count
    ).join(IssueClassification, IssueClassification.key == Issue.key) \
        .join(InitiativeAttribute, IssueClassification.initiative_key == InitiativeAttribute.initiative_key) \
        .join(issue_epic, issue_epic.key == IssueClassification.epic_key) \
        .outerjoin(issue_subtask, and_(IssueClassification.key == issue_subtask.key, issue_subtask.isSubTask.is_(True))) \
        .filter(IssueClassification.epic_key.in_(query1.union(query2))) \
        .group_by(
        InitiativeAttribute.project, InitiativeAttribute.initiative_key, InitiativeAttribute.initiative_id,
        IssueClassification.epic_key, issue_epic.status,
        issue_epic.Team, issue_epic.ClientJira, issue_epic.components
    ).order_by(InitiativeAttribute.initiative_id, IssueClassification.epic_key)

    # if os.getenv("QUERY_PLAN", '').lower() == 'yes':
    #     my_explain_plan = MyLogger()
    #     query_plan = pg_session.execute(Explain(query, analyze=True)).all()
    #     # need to check how to convert while dumping in my logger
    #     json_data = [dict(row) for row in query_plan]
    #     my_explain_plan.log_explain_plan(extra={'query_plan': json_data})
    result = query.all()
    df = pd.DataFrame(result)
    return df


# @cache.memoize(timeout=TIME_OUT)
def get_release_status_old(fixversion: list, pg_session: Session) -> pd.DataFrame:
    my_logger = MyLogger().get_logger()
    with TimeCodeBlock("get_release_status total time:"):
        issue_story = aliased(Issue)
        issue_subtask = aliased(Issue)
        issue_c = aliased(Issue)
        issue_class = aliased(IssueClassification)
        init_attr = aliased(InitiativeAttribute)

        exclude_issue_type = ['Story', 'Task', 'Bug', 'In-sprint Defects', 'UAT Defect', 'Production Defect']
        defect_issue_type = ['Bug', 'In-sprint Defects', 'UAT Defect', 'Production Defect']

        story_query = pg_session.query(func.count()).filter(issue_story.parent_key == Issue.key) \
            .filter(issue_story.issuetype.in_(['Story', 'Task']))

        # story_total = story_query
        story_todo = story_query.filter(issue_story.statusCategory == 'To Do')
        story_wip = story_query.filter(issue_story.statusCategory == 'In Progress')
        story_done = story_query.filter(issue_story.statusCategory == 'Done')

        defect_query = pg_session.query(func.count()).filter(issue_story.parent_key == Issue.key) \
            .filter(issue_story.issuetype.in_(defect_issue_type))

        # defect_total = defect_query

        defect_done = defect_query.filter(issue_story.statusCategory == 'Done')
        defect_todo = defect_query.filter(issue_story.statusCategory == 'To Do')
        defect_wip = defect_query.filter(issue_story.statusCategory == 'In Progress')

        other_query = pg_session.query(func.count()).filter(issue_story.parent_key == Issue.key) \
            .filter(~issue_story.issuetype.in_(exclude_issue_type))

        # other_total = other_query
        other_done = other_query.filter(issue_story.statusCategory == 'Done')
        other_todo = other_query.filter(issue_story.statusCategory == 'To Do')
        other_wip = other_query.filter(issue_story.statusCategory == 'In Progress')

        # story_estimated_effort = pg_session.query(
        #     cast(func.sum(issue_story.aggregatetimeoriginalestimate), Float)
        # ).filter(
        #     issue_story.parent_key == Issue.key) \
        #     .filter(issue_story.issuetype.in_(['Story', 'Task']))
        #
        # story_actual_effort = pg_session.query(
        #     cast(func.sum(issue_story.aggregatetimespent), Float)
        # ).filter(
        #     issue_story.parent_key == Issue.key) \
        #     .filter(issue_story.issuetype.in_(['Story', 'Task']))

        # subtasks_total = session.query(func.count()) \
        #     .filter(func.replace(func.ltree2text(func.subpath(IssueSubtask.path, 1, 1)), '_', '-') == Issue.key) \
        #     .filter(IssueSubtask.isSubTask.is_(True)) \
        #     .filter(func.nlevel(IssueSubtask.path) > 1)

        subtasks_query = pg_session.query(func.count()) \
            .filter(IssueClassification.epic_key == Issue.key) \
            .filter(IssueClassification.issueclass == "subtask") \
            .filter(IssueClassification.key == issue_subtask.key) \
            .filter(issue_subtask.isSubTask.is_(True))

        # subtasks_total = subtasks_query

        subtasks_done = subtasks_query.filter(issue_subtask.statusCategory == 'Done')
        subtasks_todo = subtasks_query.filter(issue_subtask.statusCategory == 'To Do')
        subtasks_wip = subtasks_query.filter(issue_subtask.statusCategory == 'In Progress')

        # issue_path = session.query(
        #     func.unnest(func.cast(func.string_to_array(func.ltree2text(Issue.path_id), '.'), ARRAY(Integer)))
        # )

        # initiative = session.query(issue_c.key).select_from(issue_c).filter(
        #     issue_c.id == (func.cast(func.string_to_array(func.ltree2text(Issue.path_id), '.'), ARRAY(Integer))).any_()
        # ).filter(issue_c.issuetype.in_(['Initiative', 'Initiative ']))

        # initiative = session.query(IssueClassification.initiative_key)\
        #     .filter(Issue.key == IssueClassification.key)

        # initiative = pg_session.query(IssueClassification.initiative_key).filter(IssueClassification.key == Issue.key)
        # initiative_id = pg_session.query(IssueClassification.initiative_id).filter(IssueClassification.key == Issue.key)
        #
        # project = pg_session.query(InitiativeAttribute.project).select_from(InitiativeAttribute) \
        #     .filter(
        #     InitiativeAttribute.initiative_id == (
        #         func.cast(func.string_to_array(func.ltree2text(Issue.path_id), '.'), ARRAY(Integer))).any_()
        # )

        # project = session.query(InitiativeAttribute.attr['project']).select_from(InitiativeAttribute) \
        #     .filter(
        #     InitiativeAttribute.initiative_id == (
        #         func.cast(func.string_to_array(func.ltree2text(Issue.path_id), '.'), ARRAY(Integer))).any_()
        # )

        # clause = [Issue.fixVersions.any(release) for release in fixversion]

        # stmt = select(
        #     IssueClassification.epic_key
        # ).select_from(Issue, IssueClassification) \
        #     .filter(Issue.key == IssueClassification.key) \
        #     .filter(Issue.issuetype.in_(['Story', 'Task', 'Epic'])) \
        #     .filter(or_(*clause)) \
        #     .group_by(IssueClassification.epic_key)

        query1 = select(
            Issue.parent_key
        ).select_from(Issue.__table__) \
            .filter(Issue.fixVersions.op('&&')(fixversion)) \
            .filter(Issue.issuetype.in_(['Story', 'Task']))

        query2 = select(
            Issue.key
        ).select_from(Issue.__table__) \
            .filter(Issue.fixVersions.op('&&')(fixversion)) \
            .filter(Issue.issuetype == 'Epic')

        query = select(
            init_attr.project,
            # initiative.label("initiative"),
            # initiative_id.label('initiative_id'),
            issue_class.initiative_key.label("initiative"),
            issue_class.initiative_id,
            # InitiativeAttribute.initiative_key.label("initiative"),
            Issue.key.label("epic"),
            Issue.statusCategory.label("status"),
            func.replace(Issue.Team, 'NaN', '').label("Team"),

            Issue.ClientJira.label("clientjira"),
            Issue.components.label("components"),
            story_todo.label("story_todo"),
            story_wip.label("story_wip"),
            story_done.label("storydone"),
            # story_total.label("storytotal"),
            other_todo.label("other_todo"),
            other_wip.label("other_wip"),
            other_done.label("otherdone"),
            # other_total.label("othertotal"),
            # story_estimated_effort.label("estimatedeffort"),
            # story_actual_effort.label("actualeffort"),
            # literal(0).label("variance"),
            subtasks_todo.label("subtask_todo"),
            subtasks_wip.label("subtask_wip"),
            subtasks_done.label("subtaskdone"),
            # subtasks_total.label("subtasktotal"),
            # InitiativeAttribute.project,
            # project.label("project"),

            defect_todo.label("defect_todo"),
            defect_wip.label("defect_wip"),
            defect_done.label("defectsdone"),
            # defect_total.label("defectstotal")
        ).select_from(Issue, issue_class, init_attr) \
            .filter(Issue.key.in_(query1.union(query2))) \
            .filter(Issue.key == issue_class.key) \
            .filter(init_attr.initiative_id == issue_class.initiative_id)
        # .order_by("initiative", Issue.Rank)

        if os.getenv("QUERY_PLAN", '').lower() == 'yes':
            my_explain_plan = MyLogger()
            query_plan = pg_session.execute(Explain(query, analyze=True)).all()
            # need to check how to convert while dumping in my logger
            json_data = [dict(row) for row in query_plan]
            my_explain_plan.log_explain_plan(extra={'query_plan': json_data})

        with TimeCodeBlock("get_release_status Query Execution"):
            result = pg_session.execute(query).all()
            # df = pd.read_sql(query, pg_session.bind)

        # with TimeCodeBlock("get_release_status populating df"):
        #     # 17
        #     df['storypercent'] = df['storydone'] / df['storytotal']
        #     # 18
        #     df['otherpercent'] = df['otherdone'] / df['othertotal']
        #     # 19
        #     df['subtaskpercent'] = df['subtaskdone'] / df['subtasktotal']
        #     # 20
        #     df['defectspercent'] = df['defectsdone'] / df['defectstotal']
        #     # 21
        #     df['variance'] = (df['actualeffort'] - df['estimatedeffort']) * 100 / df['estimatedeffort']
        df = pd.DataFrame(result)
        df.replace({np.nan: None}, inplace=True)

    return df


def get_release_status_by_standard_issue(fixversion: list, project: str) -> pd.DataFrame:
    my_logger = MyLogger()

    session = start_session(project)
    IssueSubtask = aliased(Issue)
    issue_c = aliased(Issue)
    my_logger.info(f"Version passed: {fixversion}")

    subtasks_total = session.query(func.count()) \
        .filter(IssueSubtask.parent_key == Issue.key) \
        .filter(IssueSubtask.isSubTask == True)

    subtasks_done = session.query(func.count()) \
        .filter(IssueSubtask.parent_key == Issue.key) \
        .filter(IssueSubtask.isSubTask == True) \
        .filter(IssueSubtask.statusCategory == 'Done')

    initiative = session.query(issue_c.key).select_from(issue_c).filter(
        issue_c.id == (func.cast(func.string_to_array(func.ltree2text(Issue.path_id), '.'), ARRAY(Integer))).any_()
    ).filter(issue_c.issuetype.in_(['Initiative', 'Initiative ']))

    epic = session.query(issue_c.key).select_from(issue_c).filter(
        issue_c.id == (func.cast(func.string_to_array(func.ltree2text(Issue.path_id), '.'), ARRAY(Integer))).any_()
    ).filter(issue_c.issuetype.in_(['Epic']))

    clause = [Issue.fixVersions.any(release) for release in fixversion]

    query = select(
        Issue.key, Issue.issuetype, Issue.statusCategory, Issue.components, Issue.Team,
        Issue.aggregatetimeoriginalestimate, Issue.aggregatetimespent,
        subtasks_total.label("Subtasks - Total"),
        subtasks_done.label("Subtaks - Done"),
        initiative.label("initiative"),
        epic.label("epic")
    ).select_from(Issue.__table__).filter(or_(*clause)).filter(~Issue.issuetype.in_(['Epic', 'Initiative '])) \
        .filter(Issue.isSubTask == False)

    result = session.execute(query).all()

    return pd.DataFrame(result,
                        columns=["key", "issuetype", "statuscategory", "components", "Team",
                                 "estimatedeffort", "actualeffort",
                                 "subtasktotal", "subtaskdone", "initiative", "epic"
                                 ])


def get_release_status_by_issuetype(fixversion: list, project: str) -> pd.DataFrame:
    session = start_session(project)
    clause = [Issue.fixVersions.any(release) for release in fixversion]

    query = select(
        Issue.issuetype, Issue.statusCategory,
        func.count()
    ).select_from(Issue).filter(or_(*clause)).filter(Issue.isSubTask == False) \
        .group_by(
        Issue.issuetype, Issue.statusCategory
    )

    result = session.execute(query).all()
    return pd.DataFrame(result, columns=[
        "issuetype", "statuscategory", "count"
    ])


def get_component_count_by_issuetype(project: str):
    session = start_session(project)

    query = select(
        Issue.id, Issue.key,
        Issue.issuetype,
        Issue.assignee_name, Issue.reporter_name, Issue.team_name,
        func.date_trunc('month', Issue.created).cast(Date).label("created_date"),
        case(
            (func.array_length(Issue.components, 1) > 0, 1),
            else_=literal_column("0")
        ),
        literal_column("1")
    ).select_from(Issue) \
        .filter(Issue.isSubTask.is_(False)) \
        .filter(Issue.issuetype.in_(["Story", "Task"])) \
        # .group_by(func.date_trunc('month', Issue.created).cast(Date)) \
    # .order_by(func.date_trunc('month', Issue.created).cast(Date))

    result = session.execute(query).all()
    print(get_component_count_by_issuetype.cache_info())
    return pd.DataFrame(result, columns=["id", "key", "issuetype", "assignee_name", "reporter_name", "team_name",
                                         "created_month", "components_meeting_sla", "rowno"])


def get_subtasks_with_no_originalestimates(project: str):
    session = start_session(project)
    query = select(
        func.date_trunc('month', Issue.created).cast(Date).label("created_date"),
        Issue.issuetype,
        func.count()
    ).select_from(Issue.__table__).filter(Issue.isSubTask == True) \
        .filter(~Issue.issuetype.in_(
        ['Ceremonies', 'Release Notes', 'Meetings/Walkthrough', 'QA Task',
         'Management', 'Production Support', 'Peer Support', 'Project Management', 'Training']
    )) \
        .filter((Issue.originalestimate == 0) | (Issue.originalestimate == None)) \
        .group_by(
        Issue.issuetype, func.date_trunc('month', Issue.created).cast(Date)
    ).order_by(
        func.date_trunc('month', Issue.created).cast(Date)
    )
    result = session.execute(query).all()
    return pd.DataFrame(result, columns=["created", "issuetype", "count"])


def get_dev_subtask_details(project: str):
    session = start_session(project)
    stmt = select(Issue.id, Issue.key, Issue.status, Issue.statusCategory, Issue.assignee_name, Issue.team_name,
                  func.date_trunc('month', Issue.created).cast(Date),
                  Issue.timeoriginalestimate, Issue.timespent,
                  func.round(
                      (Issue.timespent - Issue.timeoriginalestimate) / func.nullif(Issue.timeoriginalestimate, 0), 2),
                  case(
                      (Issue.timeoriginalestimate < 8, 1),
                      else_=literal_column("0")
                  ),
                  case(
                      (Issue.timespent < 8, 1),
                      else_=literal_column("0")
                  )
                  ).select_from(Issue.__table__) \
        .filter(
        Issue.issuetype.in_(['Analysis', 'Coding', 'Sub-task', 'Code Review', 'Unit Test', 'Design Review', 'Design'])) \
        .filter(~Issue.status.in_(['Cancelled']))

    result = session.execute(stmt).all()
    df = pd.DataFrame(result, columns=['id', 'key', 'status', 'statusCategory', 'assignee', 'team_name',
                                       'created_month', 'estimated_effort', 'actual_effort', 'actualvsestimate',
                                       'estimated_effort_lt_8', 'actual_effort_lt_8'
                                       ])
    return df


def get_subtasks_hourslogged(project: str):
    session = start_session(project)
    assignee_name = session.query(User.displayName).filter(Issue.assignee == User.accountId)
    team_name = session.query(Teams.team_name).filter(Teams.accountId == WorkLog.author) \
        .filter((WorkLog.started < Teams.endDate) | (Teams.endDate == None)) \
        .filter((WorkLog.started > Teams.startDate) | (Teams.startDate == None))

    query = select(
        Issue.key, Issue.issuetype, assignee_name.label("author"), Issue.team_name,
        func.sum(WorkLog.timeSpentSeconds / 3600).label("timelogged")
    ) \
        .select_from(WorkLog.__table__, Issue.__table__) \
        .where(Issue.id == WorkLog.issue_id) \
        .filter(Issue.isSubTask == True) \
        .filter(~Issue.issuetype.in_(
        ['Ceremonies', 'Release Notes', 'Meetings/Walkthrough', 'QA Task',
         'Management', 'Production Support', 'Peer Support', 'Project Management', 'Training']
    )) \
        .group_by(
        Issue.key, Issue.assignee, Issue.issuetype, "author", Issue.team_name
    ) \
        .having(func.sum(WorkLog.timeSpentSeconds / 3600) > 8)

    result = session.execute(query).all()
    print(f'cache: {get_subtasks_hourslogged.cache_info()}')
    return pd.DataFrame(result, columns=["key", "issuetype", "author", "teamname", "timelogged"])


def get_ts_compliance(project: str):
    session = start_session(project)

    query = select(
        WorkLog.issue_key, WorkLog.team_name, WorkLog.author_name,
        WorkLog.created, WorkLog.started, func.date_trunc('month', WorkLog.created).cast(Date),
    ).select_from(WorkLog.__table__).filter(WorkLog.created > '2021-10-31')

    result = session.execute(query).all()

    df = pd.DataFrame(result, columns=["key", "team_name", "author", "created", "started", "created_month"])

    df["business_days"] = np.busday_count(
        df['started'].values.astype('datetime64[D]'),
        df['created'].values.astype('datetime64[D]')
    )
    # inclusive{“both”, “neither”, “left”, “right”}

    df['started'] = df['started'].dt.date
    df['created'] = df['created'].dt.date

    df['sla-sameday'] = np.select(
        [
            df["business_days"].between(0, 1, inclusive="left"),
        ],
        [
            1
        ],
        default=0
    )
    df['sla-nextday'] = np.select(
        [
            df["business_days"].between(1, 2, inclusive="left"),
        ],
        [
            1
        ],
        default=0
    )
    df['sla-missed'] = np.select(
        [
            df["business_days"].between(0, 2, inclusive="left"),
        ],
        [
            0
        ],
        default=1
    )
    return df


def get_teams_details(prj: str, team_name: str = None, account_id: str = None) -> pd.DataFrame:
    """
    Get the details of the team and enrich the information by joining it with User table df
    Args:
        prj: JIRA project key (in lower case)
        team_name: Name of the team
        account_id: JIRA accountId of individual user

    Returns: Dataframe joining Teams and corresponding User table details
    """
    session = start_session(prj)
    team = aliased(Teams)
    df_user = get_user_detail()

    if team_name is not None:
        result = session.query(team).select_from(Teams, team) \
            .filter(Teams.accountId == team.accountId) \
            .filter(Teams.team_name == team_name).all()
    elif account_id is not None:
        result = session.query(Teams).filter(Teams.accountId == account_id).all()
    else:
        print("In else")
        result = None

    if len(result) > 0:
        df = pd.DataFrame(query_to_dict(result))
        df = df.merge(df_user, how="inner", on="accountId")
    else:
        df = pd.DataFrame(None,
                          columns=['id', 'team_name', 'accountId', 'startDate', 'endDate', 'active', 'designation',
                                   'Account Type', 'Status', 'displayName', 'emailAddress'])

    session.close()
    return df


def get_initiative_attribs(prj: str) -> pd.DataFrame:
    session = start_session(prj)

    stmt1 = select(
        InitiativeAttribute.initiative_id, InitiativeAttribute.initiative_key,
        InitiativeAttribute.project, InitiativeAttribute.release, InitiativeAttribute.feature,
        Issue.summary, Issue.status
    ).select_from(InitiativeAttribute, Issue).filter(InitiativeAttribute.initiative_id == Issue.id)

    stmt2 = select(
        Issue.id, Issue.key, None, None, None, Issue.summary, Issue.status
    ).select_from(Issue) \
        .filter(Issue.issuetype.in_(['Initiative ', 'Initiative'])) \
        .filter(Issue.id.notin_(select(InitiativeAttribute.initiative_id).select_from(InitiativeAttribute)))

    stmt = union(stmt1, stmt2)
    result = session.execute(stmt).all()
    df = pd.DataFrame(result)
    df = df.sort_values(by=['initiative_id'], ascending=False)
    return df


# def get_initiative_attribute(prj: str) -> pd.DataFrame:
#     session = start_session(prj)
#     # query = select(
#     #     InitiativeAttribute.initiative_key, InitiativeAttribute.attr['project'], Issue.summary, Issue.status
#     # ).select_from(Issue, InitiativeAttribute).filter(Issue.id == InitiativeAttribute.initiative_id) \
#     #     .filter(~Issue.status.in_(['Cancelled'])).filter(~Issue.key.in_(['PLAT-581'])) \
#     #     .order_by(InitiativeAttribute.initiative_id, InitiativeAttribute.attr['project'])
#     query = select(
#         InitiativeAttribute.initiative_key, InitiativeAttribute.project, Issue.summary, Issue.status
#     ).select_from(Issue, InitiativeAttribute).filter(Issue.id == InitiativeAttribute.initiative_id) \
#         .filter(~Issue.status.in_(['Cancelled'])).filter(~Issue.key.in_(['PLAT-581'])) \
#         .order_by(InitiativeAttribute.initiative_id, InitiativeAttribute.project)
#
#     result = session.execute(query).all()
#     df = pd.DataFrame(result, columns=["key", "project", "summary", "status"])
#     return df


def get_missing_initiative_attribute(prj: str):
    session = start_session(prj)

    initiative_id = select(InitiativeAttribute.initiative_id).select_from(InitiativeAttribute)
    query = select(
        Issue.key, Issue.summary, Issue.status
    ).select_from(Issue).filter(Issue.issuetype.in_(['Initiative', 'Initiative '])) \
        .filter(~Issue.id.in_(initiative_id)
                )
    result = session.execute(query).all()
    df = pd.DataFrame(result, columns=['key', 'summary', 'status'])
    return df


def get_open_code_reviews(pg_session: Session, issue_id: str = None) -> pd.DataFrame:
    # ancestor_of can also be used for @>
    # descendant_of can be used for LTree
    user_reporter = aliased(User)
    user_assignee = aliased(User)

    query = select(
        Issue.key, Issue.summary, func.unnest(Issue.versions).label("affected"), Issue.status, Issue.priority,
        user_reporter.displayName.label('reporter'),
        user_assignee.displayName.label('assignee'),
    ).select_from(Issue, IssueClassification, InitiativeAttribute) \
        .join(user_reporter, user_reporter.accountId == Issue.reporter) \
        .outerjoin(user_assignee, Issue.assignee == user_assignee.accountId) \
        .filter(Issue.id == IssueClassification.id) \
        .filter(IssueClassification.initiative_key == InitiativeAttribute.initiative_key) \
        .filter(InitiativeAttribute.feature == 'SQL Code Review') \
        .filter(Issue.issuetype.in_(['Bug']))

    result = pg_session.execute(query).all()
    return pd.DataFrame(result)


def get_sql_code_review_items(
        pg_session: Session, versions: list = None,
        start_date: date = None, end_date: date = date.today()
):
    # Define aliases for the tables
    issue_classification = aliased(IssueClassification)
    initiative_attribute = aliased(InitiativeAttribute)
    issue = aliased(Issue)
    user = aliased(User)
    user_assignee = aliased(User)
    change_log = aliased(ChangeLog)

    subquery_last_assignment = (
        pg_session.query(
            func.max(
                func.timezone('UTC', ChangeLog.created)
            ).label('latest_created')
        ).select_from(ChangeLog)
        .filter(ChangeLog.issue_key == change_log.issue_key)
        .filter(ChangeLog.field == 'assignee')
    )

    query = select(
        issue.key,
        issue.summary,
        issue.description_markdown,
        issue.Team,
        issue.status,
        user.displayName.label('reporter'),
        user_assignee.displayName.label('assignee'),
        change_log.fromString.label("last_assignee"),
        func.timezone('UTC', issue.created).label('created')
    ).select_from(initiative_attribute) \
        .join(issue_classification, initiative_attribute.initiative_id == issue_classification.initiative_id) \
        .join(issue, issue_classification.id == issue.id) \
        .join(user, user.accountId == issue.reporter) \
        .outerjoin(user_assignee, issue.assignee == user_assignee.accountId) \
        .join(change_log,
              and_(
                  change_log.issue_key == issue.key,
                  func.timezone('UTC', change_log.created) == subquery_last_assignment.scalar_subquery(),
                  change_log.field == 'assignee'
              )
              ) \
        .filter(initiative_attribute.feature == 'SQL Code Review') \
        .filter(func.timezone('UTC', issue.created).between(start_date, end_date))

    if versions:
        query = query.filter(issue.versions.op('&&')(versions))

    if start_date:
        query = query.filter(func.timezone('UTC', issue.created).between(start_date, end_date))

    if os.getenv("QUERY_PLAN", None):
        my_logger = MyLogger()
        query_plan = pg_session.execute(Explain(query, analyze=True)).all()
        print(query_plan)
        # need to check how to convert while dumping in my logger
        # json_data = [dict(row) for row in query_plan]
        # my_logger.log_explain_plan(extra={'query_plan': json_data})
        my_logger.log_explain_plan(f'{query_plan}')

    result = pg_session.execute(query).all()
    return result


class TimeZone(Base):
    __tablename__ = "pg_settings"
    name = Column(TEXT, primary_key=True)
    setting = Column(TEXT)
    __table_args__ = (
        {
            'schema': 'pg_catalog',
            'extend_existing': True
        }
    )


def get_db_timezone() -> pd.DataFrame:
    session = start_session()
    query = select(
        TimeZone.name, TimeZone.setting
    ).select_from(TimeZone).filter(TimeZone.name == 'TimeZone')

    result = session.execute(query).all()
    df = pd.DataFrame(result, columns=["name", "timezone"])
    return df


class IssueTypeCustom(enum.Enum):
    initiative = 1
    epic = 2
    standard = 3
    subtask = 4


def shift_column_key(row):
    if row['level'] == 1 and row['issuetype_classification'] == "subtask":
        # return [None, None, None, row['initiative_key_new']]
        return pd.DataFrame(row.shift(periods=3, axis=1))


def get_issue_hierarchy(prjkey: str = 'plat'):
    my_logger = MyLogger().get_logger()
    start_time = time.time_ns()
    with start_session(schema_name=prjkey) as session:
        topq = session.query(Issue.id, Issue.key, Issue.issuetype, Issue.isSubTask, Issue.parent_key,
                             literal(1).label('level'),
                             case((Issue.issuetype.in_(['Initiative ', 'Initiative']), 'initiative'),
                                  (Issue.issuetype == 'Epic', 'epic'),
                                  (Issue.isSubTask.is_(True), 'subtask'),
                                  else_="standard"
                                  ).label("issuetype_classification"),
                             func.text2ltree(cast(Issue.id, TEXT)).label("path_id"),
                             func.text2ltree(func.replace(Issue.key, "-", "_")).label("path_key")

                             )
        topq = topq.filter(Issue.parent_key.is_(None))
        topq = topq.cte('cte', recursive=True)

        bottomq = session.query(Issue.id, Issue.key, Issue.issuetype, Issue.isSubTask, Issue.parent_key,
                                topq.c.level + 1,
                                case((Issue.issuetype.in_(['Initiative ', 'Initiative']), 'initiative'),
                                     (Issue.issuetype == 'Epic', 'epic'),
                                     (Issue.isSubTask.is_(True), 'subtask'),
                                     else_="standard"
                                     ).label("issuetype_classification"),
                                topq.c.path_id.op('||')(func.text2ltree(cast(Issue.id, TEXT))),
                                topq.c.path_key.op('||')(func.text2ltree(func.replace(Issue.key, "-", "_")))
                                )
        bottomq = bottomq.join(topq, Issue.parent_key == topq.c.key)
        recursive_q = topq.union_all(bottomq)
        df = pd.read_sql(session.query(recursive_q).statement, session.bind)
        statement_plan = session.execute(Explain(session.query(recursive_q).statement, analyze=True)).fetchall()

        if os.getenv('PRINT_EXECUTION_PLAN'):
            print('Execution Plan')
            print(*statement_plan, sep='\n')

    time_query = time.time_ns()
    my_logger.debug(f'Time Taken by Query: {(time_query - start_time) * pow(10, -9)}')
    # Add Columns
    columns_add = [f"{counter.name}_{i}" for i in ["id", "key"] for counter in IssueTypeCustom]
    # df[[f"{counter.name}_{i}" for i in ["id", "key"] for counter in IssueTypeCustom]] = None
    # Read more of assign
    # need to create a dictonary pair corresponding to column name
    # dff = df.assign(**dict.fromkeys(columns_add, None))
    # dff.to_csv("e:/vishal/check.csv")
    print(f"Time taken adding columns: {(time.time_ns() - time_query) * pow(10, -6)} ms")

    # Update the column values with issuetypes id & key
    # This is not working
    # for counter in IssueTypeCustom:
    #     search_criteria = (df['issuetype_classification'] == counter.name)
    #     columns = [f"{counter.name}_{i}" for i in ["id", "key"]]
    #     value = df[search_criteria][["id", "key"]]
    #
    #     df.loc[search_criteria, columns] = value

    for issue in ['initiative', 'epic', 'standard', 'subtask']:
        df[f'{issue}_id'] = df[df['issuetype_classification'] == issue]['id']
        df[f'{issue}_key'] = df[df['issuetype_classification'] == issue]['key']
    time_self = time.time_ns()
    my_logger.debug(f'Self update Time Taken: {(time.time_ns() - time_query) * pow(10, -6)} ms')

    dict_initiative = df[df['issuetype_classification'] == "initiative"][
        ['key', 'initiative_id', "initiative_key"]].set_index('key').T.to_dict('list')

    for count, field in enumerate(["initiative_id", "initiative_key"]):
        df.loc[df['issuetype_classification'] == "epic", [field]] = \
            df['parent_key'].map(lambda x: dict_initiative.get(x, [None] * 2)[count])

    time_epic = time.time_ns()
    my_logger.debug(f'Time Taken Epic: {(time_epic - time_self) * pow(10, -9)}')

    dict_epic = df[df['issuetype_classification'] == "epic"][
        ['key', 'initiative_id', "initiative_key", "epic_id", "epic_key"]].set_index('key').T.to_dict('list')

    for count, field in enumerate(["initiative_id", "initiative_key", "epic_id", "epic_key", ]):
        df.loc[df['issuetype_classification'] == "standard", [field]] = \
            df['parent_key'].map(lambda x: dict_epic.get(x, [None] * 4)[count])
    time_standard = time.time_ns()
    my_logger.debug(f'Time Taken Standard: {(time_standard - time_epic) * pow(10, -9)}')

    dict_standard = df[df['issuetype_classification'] == "standard"][
        ['key', 'initiative_id', "initiative_key", "epic_id", "epic_key", "standard_id", "standard_key"]].set_index(
        'key').T.to_dict('list')

    # Subtasks mapped to standard issue
    for count, field in enumerate(
            ["initiative_id", "initiative_key", "epic_id", "epic_key", "standard_id", "standard_key"]):
        df.loc[df['issuetype_classification'] == "subtask", [field]] = \
            df['parent_key'].map(lambda x: dict_standard.get(x, [None] * 6)[count])

    # Subtasks mapped to epic
    for count, field in enumerate(["initiative_id", "initiative_key", "epic_id", "epic_key"]):
        df.loc[(df['issuetype_classification'] == "subtask") & (pd.isna(df["standard_id"])), [field]] = \
            df['parent_key'].map(lambda x: dict_epic.get(x, [None] * 4)[count])

    # Subtasks mapped to initiative
    for count, field in enumerate(["initiative_id", "initiative_key"]):
        df.loc[(df['issuetype_classification'] == "subtask") & (pd.isna(df["epic_id"])), [field]] = \
            df['parent_key'].map(lambda x: dict_initiative.get(x, [None] * 2)[count])
    time_subtask = time.time_ns()
    my_logger.debug(f'Time Taken subtask: {(time_subtask - time_standard) * pow(10, -9)}')

    # new_key = df['path_key'].str.split(".", n=-1, expand=True)
    # df[["initiative_key_new", "epic_key_new", "standard_key_new", "subtask_key_new"]] = new_key
    # new_id = df['path_id'].str.split(".", n=-1, expand=True)
    # df[["initiative_id_new", "epic_id_new", "standard_id_new", "subtask_id_new"]] = new_id
    #
    # df[[f"{IssueTypeCustom(i).name}_id_new" for i in IssueTypeCustom]] = \
    #     df[[f"{IssueTypeCustom(i).name}_id_new" for i in IssueTypeCustom]].apply(pd.to_numeric)
    #
    # for i in IssueTypeCustom:
    #     df[f"{IssueTypeCustom(i).name}_key_new"] = df[f"{IssueTypeCustom(i).name}_key_new"].apply(lambda x: x.replace('_', '-') if x is not None else None)

    # Working prototype
    # for col in ['id', 'key']:
    #     value = df[(df['issuetype_classification'] == "subtask") & (df['level'] == 1)][[f"initiative_{col}_new", f"epic_{col}_new", f"standard_{col}_new", f"subtask_{col}_new"]].shift(periods=3, axis=1)
    #     df.loc[(df['issuetype_classification'] == "subtask") & (df['level'] == 1),
    #            [f"{IssueTypeCustom(i).name}_{col}_new" for i in IssueTypeCustom]] = value
    # shift_periods=dict(epic=1, standard=2, subtask=3)
    # for col in ['id', 'key']:
    #     for level in range(1, 3):
    #         for issue_type in IssueTypeCustom:
    #             if issue_type.value > level:
    #                 if level == 2 and issue_type.value == 4:
    #                     search_criteria = (df['issuetype_classification'] == issue_type.name) & (df['level'] == level)
    #                     value = df[search_criteria][
    #                         [f"{IssueTypeCustom(i).name}_{col}_new" for i in IssueTypeCustom if i.value > 1]].shift(
    #                         periods=level, axis=1)
    #                     df.loc[search_criteria, [f"{IssueTypeCustom(i).name}_{col}_new" for i in IssueTypeCustom if i.value > 1]] = value
    #                 else:
    #                     search_criteria = (df['issuetype_classification'] == issue_type.name) & (df['level'] == level)
    #                     value = df[search_criteria][[f"{IssueTypeCustom(i).name}_{col}_new" for i in IssueTypeCustom]].shift(periods=issue_type.value - 1, axis=1)
    #                     df.loc[search_criteria, [f"{IssueTypeCustom(i).name}_{col}_new" for i in IssueTypeCustom]] = value

    # search_criteria = ~(df['issuetype_classification'].isin(["initiative", "epic"])) & ~(df['level'].isin([1, 4]))
    # value = df[search_criteria][[f"{IssueTypeCustom(i).name}_{col}_new" for i in IssueTypeCustom]].shift(periods=1,
    #                                                                                                      axis=1)
    # df.loc[search_criteria, [f"{IssueTypeCustom(i).name}_{col}_new" for i in IssueTypeCustom]] = value
    #
    # search_criteria = ~(df['issuetype_classification'].isin(["initiative", "epic", "standard"])) & ~(df['level'].isin([1,2, 4]))
    # value = df[search_criteria][[f"{IssueTypeCustom(i).name}_{col}_new" for i in IssueTypeCustom]].shift(periods=1,
    #                                                                                                      axis=1)
    # df.loc[search_criteria, [f"{IssueTypeCustom(i).name}_{col}_new" for i in IssueTypeCustom]] = value

    # search_criteria = (df['issuetype_classification'] == "subtask") & (df['level'] == 1)

    print(f'Time taken New approach: {(time.time_ns() - time_subtask) * pow(10, -9)}')
    # for count, field in enumerate(['initiative_new', 'epic_new', 'standard_new', 'subtask_new']):
    #     df[field] = new[count]
    #  [x.split('.', expand=True) for x in df['path_key']]

    my_logger.info(f'Total Time: {(time.time_ns() - start_time) * pow(10, -9)}')
    # if os.getenv('DEBUG'):
    #     xls = WriteToExcel(df, "e:/vishal/issue_classification.xlsx", sheetname="Sheet1")
    #     xls.create()


def sort_list():
    # my_version = ['***********', '***********', '***********.03', '***********.02', '***********.03', '***********.01',  '***********', '***********.01',  '***********', '***********', '16.00.12']
    # my_version = ['17.00.01.03', '17.00.01.02.01', '17.00.01.02', 'Base Release', '17.00.01.01', '17.00.01',
    #               '***********.03']
    my_version = ['4.3.2.9', '4.3.2.8', '*******', '4.3.2.6', '4.3.2.5', '4.3.2.4', '4.3.2.3', '4.3.2.2', '4.3.2.11',
                  '4.3.2.10', '4.3.2.1', '4.3.1.2']
    my_version.sort()
    print(my_version)
    my_version.sort(reverse=True, key=lambda x: list(map(int, x.split('.'))))
    print(my_version)

    # print('***********.03'.split('.'))
    # print(my_version.index('*******'))
    # print(sorted(my_version, key=lambda item: item.split(".")))
    # my_version.sort(key=lambda item: list(map(int, item.split("."))))
    # print(my_version)


def get_team_details(pg_session: Session) -> [pd.DataFrame, dict]:
    query = pg_session.query(
        User.emailAddress,
        Teams.team_name, Teams.startDate, Teams.endDate
    ).join(Teams, User.accountId == Teams.accountId)

    res = query.all()
    # Define a named tuple for the values in the dictionary
    TeamInfo = namedtuple('TeamInfo', ['team_name', 'startDate', 'endDate'])
    # Convert the list of tuples into a dictionary
    team_dict = {}
    for email, team_name, start_date, end_date in res:
        if email in team_dict:
            team_dict[email].append(TeamInfo(team_name, start_date, end_date))
        else:
            team_dict[email] = [TeamInfo(team_name, start_date, end_date)]

    return pd.DataFrame(res), team_dict


def get_teams_by_email(pg_session: Session) -> list[tuple[str, str, date, date]]:
    query = pg_session.query(
        Teams.emailAddress,
        Teams.team_name, Teams.startDate, Teams.endDate
    ).select_from(Teams)
    # .join(Teams, User.emailAddress == Teams.emailAddress)
    res = query.all()
    return res


def test_relationship_stmt():
    print("In test_relationship_stmt")
    # Reference: https://stackoverflow.com/a/********/********
    a1 = aliased(User)
    r1 = aliased(User)

    session = start_session('plat')
    stmt = select(
        Issue.key, Issue.issuetype, IssueClassification.initiative_key, IssueClassification.epic_key,
        a1.displayName,
    ) \
        .select_from(Issue).join(Issue.classification_id) \
        .filter(Issue.key == "PLAT-9931").outerjoin(a1, Issue.assignee_rel)

    result = session.query(
        Issue.key, Issue.issuetype, IssueClassification.initiative_key, IssueClassification.standard_key,
        User.displayName, a1.displayName,
    ).select_from(Issue).join(Issue.classification_id) \
        .filter(Issue.key == "PLAT-9931") \
        .join(Issue.reporter_rel).outerjoin(a1, Issue.assignee_rel)

    result2 = session.execute(stmt).all()

    print(result.all())

    print(result2)


def test_relationship():
    session = start_session('plat')
    # query = select(
    #     Issue.key, Issue.issuetype, Issue.classification_id
    # ).select_from(Issue).filter(Issue.key == 'PLAT-9674')
    # result = session.execute(query).all()

    # result = session.query(IssueClassification.child_classification_id).options(joinedload(IssueClassification.child_classification_id, innerjoin=True))
    # result = session.query(IssueClassification.child_classification_id).select_from(IssueClassification).join(IssueClassification.child_classification_id).filter(IssueClassification.key == "PLAT-9674")
    fields = ['id', 'key', 'initiative_id']

    parent = session.query(Issue).options(selectinload(Issue.classification_id)).filter(Issue.key == "PLAT-9674").all()
    print(parent)
    result = []
    for user_obj in session.execute(
            select(Issue).options(selectinload(Issue.classification_id)).filter(Issue.key == "PLAT-9674")).scalars():
        print(type(user_obj))
        print(user_obj.classification_id.initiative_key, user_obj.key, user_obj.issuetype)

        instance = inspect(user_obj)
        items = instance.attrs.items()
        result.append([x.value for _, x in items])
        df = pd.DataFrame(result, columns=instance.attrs.keys())
        print(df['classification_id'])


def get_snowflake_id() -> int:
    """
    Get the snowflake id from db
    Returns: snowflake id
    """
    session = start_session('public')
    stmt = select(func.next_id())
    result = session.execute(stmt).all()
    return result[0][0]


def is_valid_version(version_str: str) -> bool:
    try:
        version.parse(version_str)
        return True
    except version.InvalidVersion:
        return False


def get_version_after(start: str, pg_session: Session):
    df = get_versions(pg_session)[['id', 'name']]

    # Filter out invalid version strings
    df = df[df['name'].apply(is_valid_version)]

    df = df[
        df[["id", "name"]].apply(lambda x: version.parse(str(x[1])) > version.parse(start), axis=1)
    ].reset_index()

    return df


def get_version_range(from_version: str, to_version: str, pg_schema: Session):
    # Source: https://stackoverflow.com/questions/11887762/how-do-i-compare-version-numbers-in-python
    df = get_versions(pg_schema)[['name']]

    # Filter out invalid version strings
    df = df[df['name'].apply(is_valid_version)]

    df = df[
        df[["name"]].apply(lambda x:
                           version.parse(from_version) < version.parse(str(x[0])) <= version.parse(to_version),
                           axis=1)
    ].reset_index()
    return df


def version_sort(x: list):
    x = ','.join(x)
    # return sorted(x, key=lambda v: [int(x) for x in v.split('.')])
    return [int(i) for i in x.split(',')]


def get_epic_details_in_fixversion(fixversion: list, project: str, pg_session: Session):
    with pg_session as session:
        clause = [Issue.fixVersions.any(release) for release in fixversion]
        stmt = select(
            func.row_number().over(order_by=Issue.key).label("S.No."),
            InitiativeAttribute.project,
            Issue.ClientJira.label("Cookie or CARD#"),
            Issue.summary.label('Summary'), Issue.key.label("Internal JIRA#"),
            literal("").label("Feature Owner"),
            Issue.fixVersions.label("Fix Version"),
            literal("").label("Remarks"),
            Issue.initiated_by.label("Initiated By"),
            Issue.change_risk.label("Risk Level"),
            Issue.components.label("Components"),
            Issue.category_type.label("Issue Type"),
            literal("").label("PERF Testing Required."),
            # literal("").label("SQL Object Names"),
        ).select_from(Issue, IssueClassification, InitiativeAttribute) \
            .filter(Issue.id == IssueClassification.id) \
            .filter(or_(*clause)) \
            .filter(Issue.issuetype == 'Epic') \
            .filter(IssueClassification.initiative_id == InitiativeAttribute.initiative_id)
        df = pd.read_sql(stmt, session.bind)
        df.fillna('', inplace=True)
        df['Components'] = [', '.join(map(str, x)) for x in df['Components']]
        # df['Fix Version'] = [', '.join(map(str, x)) for x in df['Fix Version']]
        df['Cookie or CARD#'] = df['Cookie or CARD#'].apply(lambda x: ', '.join(map(str, x)))
    if df.shape[0] == 0:
        df.loc[0] = ""

    # Sort individual row values
    # df.sort_values(by='Fix Version', inplace=True, key=lambda x: x.str.split('.'), ascending=True)

    # sort the dataframe column
    df['Fix Version'] = df['Fix Version'].apply(sorted)
    df = df.sort_values(by=['Fix Version'], key=lambda x: x)

    # Should work if dataframe has one list value.
    # df.sort_values(by='Fix Version', inplace=True, key=lambda x: version_sort(x), ascending=True)
    # df = df.sort_values(by='Fix Version', key=lambda x: sorted(x, key=lambda v: [int(i) for i in v.split('.')]))
    # df = df.sort_values(by='Fix Version', key=lambda x: sorted(x, key=lambda v: [list(map(int, i.split('.'))) for i in x]))
    return df


def get_bug_details_in_fixversion(fixversion: list, project: str, pg_session: Session):
    user_reporter = aliased(User)
    user_assignee = aliased(User)

    with pg_session as session:
        clause = [Issue.fixVersions.any(release) for release in fixversion]
        stmt = select(
            InitiativeAttribute.project,
            Issue.key.label("Internal JIRA#"),
            Issue.summary.label("Summary"),
            Issue.issuetype, Issue.status,
            Issue.fixVersions.label("Fix Version"),
            Issue.Team, Issue.components.label("Components"),
            Issue.testcaseno.label("Test Case#"),
            user_assignee.displayName.label("Assigned To"),
            user_reporter.displayName.label("Reporter"),
            IssueClassification.initiative_key.label("Initiative"),
            Issue.reopen_count.label("Reopen Count"),
            Issue.priority, Issue.severity
        ).select_from(Issue, IssueClassification, InitiativeAttribute) \
            .join(user_reporter, user_reporter.accountId == Issue.reporter) \
            .outerjoin(user_assignee, user_assignee.accountId == Issue.assignee) \
            .filter(Issue.key == IssueClassification.key) \
            .filter(IssueClassification.initiative_id == InitiativeAttribute.initiative_id) \
            .filter(or_(*clause)) \
            .filter(Issue.issuetype.in_(['Bug', 'Production Defect', 'UAT Defect', 'In-sprint Defects']))

        df = pd.read_sql(stmt, session.bind)
        df['Components'] = [', '.join(map(str, x)) for x in df['Components']]
        df['Fix Version'] = [', '.join(map(str, x)) for x in df['Fix Version']]
    # Add empty row, if no rows found
    if df.shape[0] == 0:
        df.loc[0] = ""
    return df


def get_issue_epic_mismatch_in_fixversion(fixversion: list, project: str, pg_session: Session):
    epic = aliased(Issue)
    with pg_session as session:
        clause = [Issue.fixVersions.any(release) for release in fixversion]
        stmt = select(
            Issue.key
        ).select_from(Issue, IssueClassification, epic) \
            .filter(Issue.id == IssueClassification.id) \
            .filter(epic.key == IssueClassification.epic_key) \
            .filter(Issue.issuetype.not_in(
            ['Bug', 'Production Defect', 'UAT Defect', 'In-sprint Defects', 'Epic', 'Observation'])) \
            .filter(or_(*clause)) \
            .filter(Issue.fixVersions.op('&&')(epic.fixVersions))

        result = session.execute(stmt).all()
        result = [r for r, in result]

        stmt = select(
            InitiativeAttribute.project,
            Issue.key.label("Internal JIRA#"),
            Issue.summary.label("Summary"),
            Issue.issuetype, Issue.status,
            Issue.fixVersions.label("Fix Version"),
            IssueClassification.epic_key.label("Epic #"),
            epic.fixVersions.label("Epic Fix Version"),
            Issue.components.label("Components"),
        ).select_from(Issue, IssueClassification, InitiativeAttribute, epic) \
            .filter(Issue.id == IssueClassification.id) \
            .filter(IssueClassification.initiative_id == InitiativeAttribute.initiative_id) \
            .filter(epic.key == IssueClassification.epic_key) \
            .filter(Issue.issuetype.not_in(
            ['Bug', 'Production Defect', 'UAT Defect', 'In-sprint Defects', 'Epic', 'Observation'])) \
            .filter(or_(*clause))
        df = pd.read_sql(stmt, session.bind)
        df['Fix Version'] = [','.join(map(str, x)) for x in df['Fix Version']]
        df['Epic Fix Version'] = [', '.join(map(str, x)) for x in df['Epic Fix Version']]
        df['Components'] = [', '.join(map(str, x)) for x in df['Components']]

        df = df[~df["Internal JIRA#"].isin(result)]
    if df.shape[0] == 0:
        df.loc[0] = ""
    return df


def get_worklog_details_for_projection(project: str = 'plat'):
    with start_session(project) as session:
        stmt = select(
            func.date(func.timezone('UTC', WorkLog.started)).label("ds"),
            WorkLog.author,
            Issue.issuetype,
            case(
                (Issue.issuetype.in_(['Bug', 'In-sprint Defects', 'Production Defect', 'UAT Defect']), 'Bug'),
                (Issue.issuetype.in_(['Meetings/Walkthrough']), 'Meetings'),
                (Issue.issuetype.in_(['Project Management']), 'Project Management'),
                (InitiativeAttribute.project.in_(['CP_PLAT_CI']), 'Bug'),
                ((InitiativeAttribute.project == 'PS' and InitiativeAttribute.feature == "SQL Code Review"), 'Bug'),
                (
                    (InitiativeAttribute.project == 'Cookie PS' and InitiativeAttribute.feature == "Tech Risk"),
                    'Bug'
                ),
                (InitiativeAttribute.project.in_(
                    ['Jazz Enhancement', 'Cookie NEST', 'Cookie Enhancement', 'Cookie']
                ), 'Enhancement'),
                (InitiativeAttribute.project.in_(['Cookie PS', "Jazz PS", "Plat PS"]), 'PS Support'),
                (InitiativeAttribute.project.in_(['Cookie MS', "Jazz MS"]), 'Env Support'),
                (InitiativeAttribute.project.in_(['Cookie NBL', 'Jazz NBL', 'CC India']), 'NBL'),
                (InitiativeAttribute.project.in_(['Jazz Conversion']), 'Jazz Conversion'),
                (InitiativeAttribute.project.in_(['Cookie Audit']), 'Audit & Compliance'),
                else_="Other"
            ).label("classification"),
            IssueClassification.initiative_key,
            func.sum(WorkLog.timeSpentSeconds / 3600).label("y")
        ).select_from(Issue, WorkLog, IssueClassification, InitiativeAttribute) \
            .filter(Issue.id == IssueClassification.id) \
            .filter(WorkLog.issue_id == Issue.id) \
            .filter(InitiativeAttribute.initiative_id == IssueClassification.initiative_id) \
            .group_by(
            func.date(func.timezone('UTC', WorkLog.started)), WorkLog.author,
            Issue.issuetype, IssueClassification.initiative_key, InitiativeAttribute.project
        )
        df = pd.read_sql(stmt, session.bind)
    df['ds'] = pd.to_datetime(df['ds'])
    return df


def get_cc_jira_details(branch_or_package: str, pg_session: Session, isbranch: bool = True) -> pd.DataFrame:
    custom_logger = MyLogger()
    my_logger = custom_logger.get_logger()

    master_list = []

    if isbranch:
        table_name = CCJiraBranchCheckIn
        clause = [CCJiraBranchCheckIn.branch_name == branch_or_package]
    else:
        table_name = CCJiraPackageCheckIn
        clause = [CCJiraPackageCheckIn.package_name == branch_or_package]

    with TimeCodeBlock("get_cc_jira_details Total"):

        team_name = pg_session.query(
            Teams.team_name
        ).select_from(Teams) \
            .filter(User.accountId == Teams.accountId) \
            .filter(
            func.date(func.timezone('UTC', table_name.checked_in_on)).between(Teams.startDate, Teams.endDate)
        ).scalar_subquery()

        # display_name = session.query(
        #     User.displayName
        # ).select_from(User).filter(User.emailAddress == table_name.author_email).scalar_subquery()

        stmt = select(
            table_name.cc_jira,
            func.array_agg(
                func.distinct(
                    # func.concat(
                    #     func.date(func.timezone('UTC', CCJiraBranchCheckIn.checked_in_on)), '|',
                    #     CCJiraBranchCheckIn.author, '|',
                    #     func.coalesce(inner_query.scalar_subquery(), 'na')
                    # )
                    func.date(func.timezone('UTC', table_name.checked_in_on)) + '|' +
                    table_name.author
                    + '|' + User.displayName + '|'
                    + func.coalesce(team_name, 'na')
                )
            ).label('svn details')
        ).select_from(table_name, User) \
            .filter(*clause) \
            .filter(User.emailAddress == table_name.author_email) \
            .group_by(table_name.cc_jira)
        if os.getenv("QUERY_PLAN", '').lower() == 'yes':
            my_explain_plan = MyLogger()
            query_plan = pg_session.execute(Explain(stmt, analyze=True)).all()
            json_data = [dict(row) for row in query_plan]
            my_explain_plan.log_explain_plan(extra={'query_plan': json_data})
            # my_logger.debug('\n'.join(map(str, query_plan)))

        result = pg_session.execute(stmt).all()
        df1 = pd.DataFrame(result)
        if df1.shape[0] == 0:
            my_logger.info(f"{branch_or_package}: No Data Found")
            return pd.DataFrame(columns=['cc_jira', 'svn details', 'key', 'issuetype', 'status', 'fixVersions',
                                         'isSubTask', 'epic key', 'epic status', 'epic fixVersions', 'project',
                                         'release', 'feature', 'standard Key', 'standard issuetype',
                                         'standard status', 'standard fixVersions']
                                )
        # Query JIRA tables
        result = pg_session.query(IssueClassification).filter(
            IssueClassification.key.in_(df1['cc_jira'].values)).all()

        for row in result:
            node_dict = {
                'key': row.key,
                'issuetype': row.myself.issuetype,
                'status': row.myself.status,
                'fixVersions': row.myself.fixVersions,
                'isSubTask': row.myself.isSubTask,
                'clientJira': row.myself.ClientJira if row.myself.ClientJira is not None else []
                # 'clientJira': ','.join([str(elem) for elem in row.myself.ClientJira]) if row.myself.ClientJira is not None else 'NA'
            }
            if 'clientJira' in node_dict:
                node_dict['clientJira'] = ', '.join([str(elem) for elem in node_dict['clientJira']])
            standard = getattr(row, 'standard')
            if standard:
                node_dict['standard Key'] = standard.key
                node_dict['standard issuetype'] = standard.issuetype
                node_dict['standard status'] = standard.status
                node_dict['standard fixVersions'] = standard.fixVersions
                node_dict['standard ClientJira'] = standard.ClientJira if standard.ClientJira is not None else []
                # node_dict['standard ClientJira'] = ','.join([str(elem) for elem in standard.ClientJira]) if standard.ClientJira is not None else 'NA'
            if 'standard ClientJira' in node_dict:
                node_dict['standard ClientJira'] = ', '.join(
                    [str(elem) for elem in node_dict['standard ClientJira']])

            # Check if joined row is empty
            epic_list = getattr(row, 'epic')
            if epic_list:
                for epic in epic_list:
                    node_dict['epic key'] = epic.key
                    node_dict['epic status'] = epic.status
                    node_dict['epic fixVersions'] = epic.fixVersions
                    node_dict['epic ClientJira'] = epic.ClientJira if epic.ClientJira is not None else []
                    # node_dict['epic ClientJira'] = ','.join([str(elem) for elem in epic.ClientJira]) if epic.ClientJira is not None else 'NA'
                if 'epic ClientJira' in node_dict:
                    node_dict['epic ClientJira'] = ', '.join([str(elem) for elem in node_dict['epic ClientJira']])

            init_attr_list = getattr(row, 'initiative_attr')
            if init_attr_list:
                for init_attr in init_attr_list:
                    node_dict['project'] = init_attr.project
                    node_dict['release'] = init_attr.release
                    node_dict['feature'] = init_attr.feature

            master_list.append(node_dict)
        df2 = pd.DataFrame(master_list)
        df = pd.merge(df1, df2, how='left', left_on='cc_jira', right_on='key')
        cols = ['epic ClientJira', 'standard ClientJira', 'clientJira']
        # for col in cols:
        #     df[col] = df[col].apply(lambda x: ', '.join(map(str, x)))
        #     df[col] = df[col].fillna('')
        # df['combined'] = df[cols].apply(lambda _row: '|'.join(_row.values.astype(str)), axis=1)
        df['combined'] = df['epic ClientJira'] + '|' + df['standard ClientJira'] + '|' + df['clientJira']
        df['key'].fillna('Not Found', inplace=True)
        df.fillna('', inplace=True)
    return df


def get_classification_test(project: str = 'plat'):
    stmt = select(
        IssueClassification
    ).select_from(IssueClassification).filter(IssueClassification.key == 'PLAT-100')
    master_list = []

    with start_session(project) as session:
        result = session.query(IssueClassification).filter(
            IssueClassification.key.in_(["PLAT-75638", "PLAT-100"])).all()

        for row in result:
            node_list = [row.key]
            # Check if joined row is empty
            if getattr(row, 'epic'):
                for epic in row.epic:
                    node_list.append(epic.key)
            else:
                node_list.append('')

            if getattr(row, 'initiative_attr'):
                for init_attr in row.initiative_attr:
                    node_list.append(init_attr.release)
                    node_list.append(init_attr.project)
            else:
                node_list.extend(['', ''])

            master_list.append(node_list)
    print(master_list)


def get_jira_users_test():
    columns = [Issue.key, Issue.summary, User.displayName, Issue.issuetype, Issue.summary]
    with start_session('plat') as db_session:
        result = db_session.query(User).filter(User.emailAddress == '<EMAIL>').all()
        reporter = db_session.query(User).filter_by(emailAddress='<EMAIL>').first()
        issues = reporter.issues_by_reporter
        _df = pd.DataFrame([row.__dict__ for row in issues])
        _df.to_csv("e:/vishal/issues.csv")

        selected_columns = [Issue.key, Issue.created, Issue.assignee, Issue.reporter, User.displayName]

        issues = db_session.query(*selected_columns).join(Issue.reporter_user).filter(
            and_(User.emailAddress == '<EMAIL>', Issue.reporter == User.accountId)).all()

        _df = pd.DataFrame(issues)
        _df.to_csv("e:/vishal/issues_2nd.csv")
        return result


def get_ancestors(issue) -> list:
    ancestors = []
    while issue.parent is not None:
        issue = issue.parent
        ancestors.append(issue)
    return ancestors


def get_descendants(issue) -> list:
    descendants = []
    for child in issue.children:
        descendants.append(child)
        descendants.extend(get_descendants(child))
    return descendants


def get_trial_by_fire():
    my_logger = MyLogger()
    with start_session('plat') as db_session:
        user = db_session.query(User).filter(User.emailAddress == '<EMAIL>').first()
        if user:
            assigned_issues = user.assigned_issues
            for issue in assigned_issues:
                print(issue.key)
            # assigned_issues_count = len(user.issues_as_assignee)
            print(user.reported_issues.count())
            print(type(user.assigned_issues))
            print(f'assigned issues count: {len(user.assigned_issues)}')
            print(user.issue_info())
            # print(user.teams)

        issue = db_session.query(Issue).filter_by(key='PLAT-93209').one()
        print(f'{issue.key} - {issue.reporter_user.displayName}')
        print(f'{issue.derived_team_name_by_created_by_assignee}')
        children = issue.children
        parent = issue.parent

        print("Getting Ancestors")
        ancestor = get_ancestors(issue)
        for value in ancestor:
            print(value.key)
        print("Getting Decendants")
        mylogger = MyLogger()
        mylogger.info("print decendant")

        descendant = get_descendants(issue)
        for value in descendant:
            print(value.key)


# def get_all_active_versions(pg_session: sessionmaker()) -> list:

def split_item(item):
    # Split the item into parts based on numeric boundaries
    parts = re.split(r'(\d+)', item)

    # Iterate over the parts and convert numeric parts to integers, leaving non-numeric parts as strings
    return [int(part) if part.isdigit() else part for part in parts if part]


def get_all_active_versions(pg_session) -> list:
    """
    Returns all Version.name that are not released and not archived
    Args:
        schema_name:
        session_factory:


    Returns: list of version names ['16.00.03', '16.00.03.04']

    """

    selected_columns = [Versions.name]
    # returns list of tuple like [('16.00.03',), ('23.8.10.5_P',)]
    res = pg_session.query(*selected_columns) \
        .filter(Versions.archived.is_(False)) \
        .filter(Versions.released.is_(False)).all()

    # Converts [('16.00.03',), ('16.00.03.04',)] to ['16.00.03', '16.00.03.04']
    # list(map(lambda x: x[0], result))
    # Extract elements from tuples
    list_of_strings = [items[0] for items in res]
    # Sort the list using the custom key function
    sorted_list = sorted(list_of_strings, key=split_item, reverse=True)

    # Below logic also works. Keep it in case any issue encountered in future
    # sorted_list: list = sorted(
    #     # Convert list of tuples to list
    #     [items.replace('_P', '.001').replace('0X', '002').replace('_Delta', '.003') for t in res for items in t],
    #     # Sort the resulting list in descending order
    #     key=lambda x: list(map(int, x.split('.'))),
    #     reverse=True
    # )
    # sorted_list = [item.replace('.001', '_P').replace('002', '0X').replace('.003', '_Delta') for item in sorted_list]

    return sorted_list


def check_worklog():
    with start_session('plat') as db_session:
        # query = pg_session.query(WorkLog).filter(WorkLog.issue_key == 'PLAT-80354').one()
        # print(query.issue)

        query = db_session.query(WorkLog).options(joinedload(WorkLog.issue)).filter(
            WorkLog.issue_key == 'PLAT-80354').all()
        for worklog in query:
            # print(worklog.issue.assignee_user)
            print(worklog.team_started)
            print(worklog.team_created)


def version_check(fixVersion: list, branch: str):
    sorted_versions = sorted(fixVersion, key=LooseVersion)
    main_branch = re.sub(r"[^\d.]", "", branch)

    family_versions = [v for v in sorted_versions if v.startswith(main_branch)]
    smaller_version = [v for v in sorted_versions if LooseVersion(v) < LooseVersion(main_branch)]
    higher_version = [v for v in sorted_versions if LooseVersion(v) > LooseVersion(main_branch)]

    if family_versions:
        return 'SAME'
    elif smaller_version:
        return 'BACKPORT'
    else:
        return 'HIGHER'


def get_svn_branch_details():
    with start_session('plat') as db_session:
        stmt = select(
            CCJiraBranchCheckIn.cc_jira, Issue.fixVersions, CCJiraBranchCheckIn.branch_name
        ) \
            .filter(CCJiraBranchCheckIn.branch_name == 'Plat_22.5.5.1') \
            .filter(CCJiraBranchCheckIn.cc_jira == Issue.key) \
            .filter(func.array_length(Issue.fixVersions, 1) > 0)
        result = db_session.execute(stmt).all()
        df = pd.DataFrame(result)
        df['Version Check'] = df.apply(lambda x: version_check(x['fixVersions'], x['branch_name']), axis=1)


# **************
# Start Reports section


def get_bug_summary(versions: list, db_session, fix_version: bool = False) -> pd.DataFrame:
    """
    Fetch bugs by
        Affects Version or Fix Version
        status = [Open, In Progress] or [Fixed, Rejected]
        project = Cookie Enhancement & CP_PLAT_CI or Cookie NEST or Jazz Enhancement
        feature = not in NEST Regression Testing, Cookie Regression Testing, Jazz Regression Testing
    Args:
        versions: list of fixVersion or Affected Version
        db_session: database session
        fix_version: bool flag to determine if query needs to be run based on Affected Version or fixVersion
        default is to query based on Affected Version

    Returns: dataframe

    """
    if fix_version:
        clause = [Issue.fixVersions.any(release) for release in versions]
        status_clause = Issue.status.in_(['Fixed', 'Rejected'])
    else:
        clause = [Issue.versions.any(release) for release in versions]
        status_clause = Issue.status.in_(['Open', 'In Progress'])
    stmt = select(
        Issue.key.label("Internal JIRA#"),
        Issue.priority,
        Issue.severity,
        func.coalesce(Issue.Team, 'Not Mapped').label("Team")
    ).select_from(Issue, IssueClassification, InitiativeAttribute) \
        .filter(or_(*clause)) \
        .filter(Issue.issuetype.in_(['Bug', 'Production Defect', 'UAT Defect', 'In-sprint Defects'])) \
        .filter(status_clause) \
        .filter(Issue.id == IssueClassification.id) \
        .filter(IssueClassification.initiative_id == InitiativeAttribute.initiative_id) \
        .filter(InitiativeAttribute.project.in_(['Cookie Enhancement', 'CP_PLAT_CI', 'Cookie NEST']))

    df = pd.read_sql(stmt, db_session.bind)
    return df


def create_string(open, fixed, misc, closed, total):
    ret_value = ""

    # if open > 0:
    #     ret_value += f"With Dev\t{open}\n"
    # if fixed > 0:
    #     ret_value += f"With QA\t{fixed}\n"
    # if misc > 0:
    #     ret_value += f"Misc\t{misc}\n"
    #
    # if closed > 0:
    #     ret_value += f"Closed\t{closed}\n"
    #
    # if total > 0:
    #     ret_value += f"Total\t{total}"

    ret_value = f'{open}|{fixed}|{misc}|{closed}'

    return ret_value


def report_bug_summary_modified(versions: list, db_session, by_fix_version: bool = False):
    if by_fix_version:
        clause = [Issue.fixVersions.any(release) for release in versions]
    else:
        clause = [Issue.versions.any(release) for release in versions]

    query = select(
        Issue.key,
        Issue.priority,
        func.coalesce(Issue.Team, 'Not Mapped').label("Team"),
        case(
            (Issue.status.in_(['Open', 'In Progress']), 'DEV'),
            (Issue.status.in_(['Fixed', 'Rejected']), 'QA'),
            (Issue.status.in_(['Closed', 'Cancelled']), 'Closed'),
            (Issue.status.in_(['On Hold']), 'ON_HOLD'),
            else_=Issue.status
        ).label('custom_status'),
        # Issue.status,
        InitiativeAttribute.project,
        InitiativeAttribute.release,
        InitiativeAttribute.feature
    ).select_from(Issue, IssueClassification, InitiativeAttribute) \
        .filter(or_(*clause)) \
        .filter(Issue.issuetype.in_(['Bug', 'Production Defect', 'UAT Defect', 'In-sprint Defects'])) \
        .filter(Issue.id == IssueClassification.id) \
        .filter(IssueClassification.initiative_id == InitiativeAttribute.initiative_id)
    result = db_session.execute(query).all()
    return result


def update_gs_jira(df: pd.DataFrame, pg_session):
    try:
        upsert(pg_session, GSJiraIssues, df, 'client_jira_no')
        pg_session.commit()
    except Exception:
        raise


def get_gs_jira(client_jira: list, pg_session):
    query = select(
        GSJiraIssues.client_jira_no,
        GSJiraIssues.summary.label('gs_summary'),
        case(
            (GSJiraIssues.effort_estimates.is_(None), literal(0)),
            (GSJiraIssues.effort_estimates.op('~')(r'^[0-9]*\.?[0-9]+$'), GSJiraIssues.effort_estimates.cast(Numeric)),
            else_=literal(0)
        ).label('effort_estimates'),
        # GSJiraIssues.effort_estimates,
        GSJiraIssues.status.label('gs_status'),
        GSJiraIssues.fix_versions.label('gs_fixversion')
    ).select_from(GSJiraIssues).filter(GSJiraIssues.client_jira_no.in_(client_jira))
    row_set = pg_session.execute(query).all()
    return pd.DataFrame(row_set)


def get_issue_details(issue_key: list, pg_session):
    issue_epic = aliased(Issue)
    issue_standard = aliased(Issue)

    query = select(
        Issue.key.label("cc_issue_key"),
        Issue.summary.label("cc_issue_summary"),
        Issue.issuetype.label("cc_issuetype"),
        Issue.status.label("cc_issue_status"),
        Issue.fixVersions,
        Issue.versions,
        Issue.priority, Issue.severity,
        issue_epic.key.label("cc_epic_key"),
        issue_epic.summary.label("cc_epic_summary"),
        issue_standard.key.label("standard_key"),
        issue_standard.issuetype.label("standard_issuetype"),
        issue_standard.aggregatetimespent,
        InitiativeAttribute.feature.label("custom_feature"),
        issue_epic.components.label("cc_epic_components"),
        issue_epic.fixVersions.label("cc_epic_fix_version")
    ).select_from(Issue) \
        .join(IssueClassification, Issue.key == IssueClassification.key) \
        .join(issue_epic, IssueClassification.epic_key == issue_epic.key) \
        .join(issue_standard, IssueClassification.standard_key == issue_standard.key) \
        .join(InitiativeAttribute, IssueClassification.initiative_key == InitiativeAttribute.initiative_key) \
        .filter(Issue.key.in_(issue_key))
    result_set = pg_session.execute(query).all()
    return pd.DataFrame(result_set)


def get_epic_details(issue_key: list, pg_session):
    query = select(
        Issue.key,
        Issue.summary
    ).filter(Issue.key.in_(issue_key))

    result_set = pg_session.execute(query).all()
    return pd.DataFrame(result_set)


def get_nlp_taining_data(pg_session: Session):
    stmt = select(NLPTrainingData.summary, NLPTrainingData.label).select_from(NLPTrainingData)
    return pg_session.execute(stmt).fetchall()


def report_bug_summary(versions: list, db_session, by_fix_version: bool = False):
    """
    Fetch bugs by
        Affects Version or Fix Version
        status = [Open, In Progress] or [Fixed, Rejected]
        project = Cookie Enhancement & CP_PLAT_CI or Cookie NEST or Jazz Enhancement
        feature = not in NEST Regression Testing, Cookie Regression Testing, Jazz Regression Testing
    Args:
        versions: list of fixVersion or Affected Version
        db_session: database session
        by_fix_version: bool flag to determine if query needs to be run based on Affected Version or fixVersion
        default is to query based on Affected Version

    Returns: dataframe

    """
    if by_fix_version:
        clause = [Issue.fixVersions.any(release) for release in versions]
    else:
        clause = [Issue.versions.any(release) for release in versions]

    open_ticket = select(
        Issue.priority,
        func.coalesce(Issue.Team, 'Not Mapped').label("Team"),
        func.count(1).label("Open"),
    ).select_from(Issue, IssueClassification, InitiativeAttribute) \
        .filter(or_(*clause)) \
        .filter(Issue.issuetype.in_(['Bug', 'Production Defect', 'UAT Defect', 'In-sprint Defects', 'Observation'])) \
        .filter(Issue.status.in_(['Open', 'In Progress'])) \
        .filter(Issue.id == IssueClassification.id) \
        .filter(IssueClassification.initiative_id == InitiativeAttribute.initiative_id) \
        .filter(InitiativeAttribute.project.in_(['Cookie Enhancement', 'CP_PLAT_CI', 'Cookie NEST'])) \
        .filter(InitiativeAttribute.feature.not_in(
        ['NEST Regression Testing', 'Cookie Regression Testing', 'Jazz Regression Testing'])
    ).group_by(Issue.priority, Issue.Team)

    fixed_ticket = select(
        Issue.priority,
        func.coalesce(Issue.Team, 'Not Mapped').label("Team"),
        func.count(1).label("Fixed"),
    ).select_from(Issue, IssueClassification, InitiativeAttribute) \
        .filter(or_(*clause)) \
        .filter(Issue.issuetype.in_(['Bug', 'Production Defect', 'UAT Defect', 'In-sprint Defects'])) \
        .filter(Issue.status.in_(['Fixed', 'Rejected', 'Verified'])) \
        .filter(Issue.id == IssueClassification.id) \
        .filter(IssueClassification.initiative_id == InitiativeAttribute.initiative_id) \
        .filter(InitiativeAttribute.project.in_(['Cookie Enhancement', 'CP_PLAT_CI', 'Cookie NEST'])) \
        .filter(InitiativeAttribute.feature.not_in(
        ['NEST Regression Testing', 'Cookie Regression Testing', 'Jazz Regression Testing'])
    ).group_by(Issue.priority, Issue.Team)

    close_ticket = select(
        Issue.priority,
        func.coalesce(Issue.Team, 'Not Mapped').label("Team"),
        func.count(1).label("Closed"),
    ).select_from(Issue, IssueClassification, InitiativeAttribute) \
        .filter(or_(*clause)) \
        .filter(Issue.issuetype.in_(['Bug', 'Production Defect', 'UAT Defect', 'In-sprint Defects'])) \
        .filter(Issue.status.in_(['Closed', 'Cancelled'])) \
        .filter(Issue.id == IssueClassification.id) \
        .filter(IssueClassification.initiative_id == InitiativeAttribute.initiative_id) \
        .filter(InitiativeAttribute.project.in_(['Cookie Enhancement', 'CP_PLAT_CI', 'Cookie NEST'])) \
        .filter(InitiativeAttribute.feature.not_in(
        ['NEST Regression Testing', 'Cookie Regression Testing', 'Jazz Regression Testing'])
    ).group_by(Issue.priority, Issue.Team)

    misc_ticket = select(
        Issue.priority,
        func.coalesce(Issue.Team, 'Not Mapped').label("Team"),
        func.count(1).label("Misc"),
    ).select_from(Issue, IssueClassification, InitiativeAttribute) \
        .filter(or_(*clause)) \
        .filter(Issue.issuetype.in_(['Bug', 'Production Defect', 'UAT Defect', 'In-sprint Defects'])) \
        .filter(Issue.status.not_in(['Fixed', 'Rejected', 'Open', 'In Progress', 'Cancelled', 'Closed'])) \
        .filter(Issue.id == IssueClassification.id) \
        .filter(IssueClassification.initiative_id == InitiativeAttribute.initiative_id) \
        .filter(InitiativeAttribute.project.in_(['Cookie Enhancement', 'CP_PLAT_CI', 'Cookie NEST'])) \
        .filter(InitiativeAttribute.feature.not_in(
        ['NEST Regression Testing', 'Cookie Regression Testing', 'Jazz Regression Testing'])
    ).group_by(Issue.priority, Issue.Team)

    total_count = select(
        Issue.priority,
        func.coalesce(Issue.Team, 'Not Mapped').label("Team"),
        func.count(1).label("Total"),
    ).select_from(Issue, IssueClassification, InitiativeAttribute) \
        .filter(or_(*clause)) \
        .filter(Issue.issuetype.in_(['Bug', 'Production Defect', 'UAT Defect', 'In-sprint Defects'])) \
        .filter(Issue.id == IssueClassification.id) \
        .filter(IssueClassification.initiative_id == InitiativeAttribute.initiative_id) \
        .filter(InitiativeAttribute.project.in_(['Cookie Enhancement', 'CP_PLAT_CI', 'Cookie NEST'])) \
        .filter(InitiativeAttribute.feature.not_in(
        ['NEST Regression Testing', 'Cookie Regression Testing', 'Jazz Regression Testing'])
    ).group_by(Issue.priority, Issue.Team)

    df1 = pd.read_sql(open_ticket, db_session.bind)
    df2 = pd.read_sql(fixed_ticket, db_session.bind)
    df3 = pd.read_sql(misc_ticket, db_session.bind)
    df4 = pd.read_sql(total_count, db_session.bind)
    df5 = pd.read_sql(close_ticket, db_session.bind)

    df = pd.merge(left=df4, right=df1, on=['priority', 'Team'], how='outer')
    df = pd.merge(left=df, right=df2, on=['priority', 'Team'], how='outer')
    df = pd.merge(left=df, right=df3, on=['priority', 'Team'], how='outer')
    df = pd.merge(left=df, right=df5, on=['priority', 'Team'], how='outer')
    df.sort_values('Total', ascending=False, inplace=True)
    for col in ['Open', 'Total', 'Fixed', 'Misc', 'Closed']:
        df[col].fillna(0, inplace=True)
        df[col] = df[col].astype('int16')

    # df['opt_2'] = 'With Dev:\t' + df['Open'].astype(str) + \
    #               '\nWith QA:\t' + df['Fixed'].astype(str) + \
    #               '\nMisc:\t' + df['Misc'].astype(str) + \
    #               '\nClosed:\t' + df['Closed'].astype(str) + \
    #               '\nTotal:\t' + df['Total'].astype(str)
    df['opt_2'] = df.apply(lambda x: create_string(x['Open'], x['Fixed'], x['Misc'], x['Closed'], x['Total']), axis=1)
    df.drop(columns=['Open', 'Fixed', 'Misc', 'Closed', ], inplace=True)

    # df_pivot = pd.pivot_table(df, values=['opt_2'], index=['Team'], columns=['priority'], aggfunc=lambda x: x)
    return df


@cache.memoize(timeout=SHORT_TIME_OUT)
def get_request_tracker_data(pg_session: Session, desc_only: bool = True):
    if desc_only:
        stmt = select(RequestTracker.desc).select_from(RequestTracker)
    else:
        stmt = select(
            RequestTracker.id, RequestTracker.desc, RequestTracker.category,
            RequestTracker.label, RequestTracker.confidence_percent,
        )
    return pg_session.execute(stmt).fetchall()


@cache.memoize(timeout=SHORT_TIME_OUT)
def get_ts_data_from_ms(
        conn, project_code: int, start_date: int, end_date: int,
        project_client: dict
):
    if project_client[str(project_code)] == 8033:
        sp_name = 'GenerateProjectHrsFeatureTaskEmp_PLAT'
    else:
        sp_name = 'GenerateProjectHrsFeatureTaskEmp'
    # query = conn.execute(text(f"EXEC {sp_name} {project_code}, '', {start_date}, {end_date}, 'iscprojectmanagement'"))
    sql_query = text(f"EXEC {sp_name} :project_code, '', :start_date, :end_date, 'iscprojectmanagement'")
    params = {'project_code': project_code, 'start_date': start_date, 'end_date': end_date}

    try:
        result_set = conn.execute(sql_query.bindparams(**params)).all()
        return result_set
    except DataError as e:
        print(f"DB Exception: {str(e)}")
        return None


@cache.memoize(timeout=TIME_OUT)
def get_ts_leave_details_from_ms(conn, start_date, end_date):
    print(f'start_date type = {type(start_date)}')
    query = f"""
select WeekEndDate, el.EeLogin, el.EeLogin + '@corecard.com' as "email_id", sum(th.Totalhrs) as "Totalhrs",
'Leave+Holiday' as "ProjectName"
from ISCTimeSheetMgmt..Timesheethrs th with(nolock)
inner join ISCTimeSheetMgmt..timesheetinfo ti with(nolock) on th.timecardid = ti.timecardid
inner join ISCTimeSheetMgmt..employeelogin el with(nolock) on th.EeID = el.EeID
inner join ISCTimeSheetMgmt..task t with(nolock) on th.taskcode = t.taskcode  
inner join ISCTimeSheetMgmt..week w with(nolock) on ti.WeekId = w.WeekId 
where 
th.TaskCode in (1211,1217,3806,1220,1221,1219,1202)
and ti.WeekId between {start_date} AND {end_date} 
group by WeekEndDate, el.EeLogin
    """
    return conn.execute(text(query)).all()


# @inject
# def test_function(pwd_mgr: KeePassPasswordManager = Provide[MyKeePassContainer.password_manager]):
#     print(pwd_mgr.get_password('corecard Jira'))
#     print(type(pwd_mgr))
#     print(dir(pwd_mgr))


@inject
def test_db_session(
        session_factory: AppContainer = Provide[AppContainer.session_factory],
        schema_name='plat'
):
    print(dir(session_factory))
    print(type(session_factory))
    with session_factory.db_ro().session() as db_session:
        if schema_name is not None:
            updated_engine = db_session.get_bind().execution_options(schema_translate_map={None: schema_name})
            db_session.bind = updated_engine
        stmt = db_session.query(InitiativeAttribute).all()
        print(len(stmt))


# End Reports section

def get_second_elem(x):
    if x is None:
        return None
    else:
        sorted_list = sorted(x, key=lambda y: y.split(',')[0] if y is not None else '')
        return sorted_list
        # return sorted_list[0].split(',')[1] if sorted_list[0] is not None else ''


if __name__ == '__main__':
    # Initialize keepass
    keepass = KPDatabaseOpener(
        os.getenv("DATABASE_PATH"),
        master_keyfile=os.getenv("MASTER_KEYFILE")
    )

    kpk_inst = KeePassPasswordManager(keepass)

    jira_ro_uri = DbConnectionURI(kpk_inst, read_only=True)
    ms_conn = MSSQLDatabase(kpk_inst)

    _session = Database(jira_ro_uri)

    with _session.session() as db_session:
        updated_engine = db_session.get_bind().execution_options(schema_translate_map={None: 'plat'})
        db_session.bind = updated_engine

        result = get_user_counts(db_session)
        print(result)

        result = get_issue_details(['PLAT-182524'], db_session)
        df_part = result[['priority']].copy()
        df_part['priority'] = df_part['priority'].astype(str)
        print(df_part.dtypes)
        print(df_part.to_dict("records"))

        # execution_time = timeit.timeit(
        #     lambda: get_sql_code_review_items(
        #         db_session, start_date=date(2023, 4, 1)
        #     ),
        #     number=1)
        # print(f'exec time for get_sql_code_review_items {execution_time}')

        exit()

        # dff = get_release_status(['22.10', '22.10.1', '22.10.2', '22.10.3', '22.10.4', '22.10.5'], db_session)
        execution_time = timeit.timeit(
            lambda: get_release_status_old(
                ['23.2.3', '23.2.2', '23.2.1', '23.2', '23.1.5', '23.1.4.3', '23.1.4.2', '23.1.4.1', '23.1.4', '23.1.3',
                 '23.1.2', '23.1.1', '23.1'],
                db_session
            ),
            number=1)
        print(f'exec time for get_release_status {execution_time}')

        print("Calling get_release_status_mod ")
        execution_time = timeit.timeit(
            lambda: get_release_status(
                ['23.2.3', '23.2.2', '23.2.1', '23.2', '23.1.5', '23.1.4.3', '23.1.4.2', '23.1.4.1', '23.1.4', '23.1.3',
                 '23.1.2', '23.1.1', '23.1'],
                db_session
            ), number=1)

        print(f'exec time for get_release_status_mod {execution_time}')

        df = get_release_status(
            ['23.2.3', '23.2.2', '23.2.1', '23.2', '23.1.5', '23.1.4.3', '23.1.4.2', '23.1.4.1', '23.1.4', '23.1.3',
             '23.1.2', '23.1.1', '23.1'],
            db_session
        )
        df.to_csv("e:/vishal/new.csv")

        df = get_release_status_old(
            ['23.2.3', '23.2.2', '23.2.1', '23.2', '23.1.5', '23.1.4.3', '23.1.4.2', '23.1.4.1', '23.1.4', '23.1.3',
             '23.1.2', '23.1.1', '23.1'],
            db_session
        )
        df.to_csv("e:/vishal/old.csv")

        execution_time = timeit.timeit(
            lambda: get_release_defect_status(
                ['23.2.3', '23.2.2', '23.2.1', '23.2', '23.1.5', '23.1.4.3', '23.1.4.2', '23.1.4.1', '23.1.4', '23.1.3',
                 '23.1.2', '23.1.1', '23.1'],
                db_session, 1
            ),
            number=1)
        print(f'exec time for get_release_defect_status {execution_time}')

    with _session.session() as db_session:
        execution_time = timeit.timeit(lambda: get_board_id(db_session, 'plat'), number=10)
        print(f'exec time for get_board_id {execution_time}')
        execution_time = timeit.timeit(lambda: get_team_names(db_session), number=1)
        print(f'exec time for get_team_names {execution_time}')
        execution_time = timeit.timeit(lambda: get_worklog_details('2023-03-08', '2023-03-22', db_session), number=1, )

        rows = get_users(db_session)
        account_type_counts = Counter(
            [(row.accountType, row.active) for row in rows])

        for (account_type, status), count in account_type_counts.items():
            print(account_type, status, count)

        result = report_bug_summary_modified(
            ['22.10.1', '22.10.2'],
            db_session, by_fix_version=False
        )
        columns = ['key', 'priority', 'team', 'status', 'project', 'release', 'feature']
        df = pd.DataFrame(result, columns=columns)
        df.to_csv("e:/vishal/original.csv")
        pivot = pd.pivot_table(df, index=['team'], columns=['priority'], values=['status'], aggfunc=lambda x: x,
                               fill_value="")
        pivot.to_csv("e:/vishal/original_pivot.csv")

        # Summarize result with list comprehension
        team_priority_dict = {}
        for item in result:
            team, priority = item[2], item[1]
            team_priority_dict[(team, priority)] = {'DEV': 0, 'QA': 0, 'Closed': 0, 'ON_HOLD': 0}
        # Update counts for each combination of team and priority based on status
        for item in result:
            team, priority, status = item[2], item[1], item[3]
            team_priority_dict[(team, priority)][status] += 1

        # Print the counts for each combination of team and priority
        df = pd.DataFrame.from_dict(team_priority_dict)

        df_transform = df.T
        df_transform.to_csv("e:/vishal/trial.csv")
    exit(0)

    container = MyKeePassContainer()
    container.wire(modules=[__name__])
    app_container = AppContainer()
    app_container.wire(modules=[__name__])
    print(container.providers)
    # test_function()
    desired_width = 320

    pd.set_option('display.width', desired_width)

    np.set_printoptions(linewidth=desired_width)

    pd.set_option('display.max_columns', 10)
    password_manager = MyKeePassContainer().password_manager()
    connection_string = DbConnectionURI(password_manager)
    print(connection_string.pg_db_url)

    test_db_session(schema_name='plat')

    # print(container.db_conn_uri().pg_db_url)
    # print(container.db().get_db_schema())
    # print(container.db().session())

    # db_container = AppContainer.app_factory()
    # print(f'main {db_container.db().session()}')
    # print(type(db_container))
    # print(dir(db_container))
    # print(Provide[AppContainer.app_factory.provides])
    # print(type(Provide[AppContainer.app_factory]))
    # print(dir(Provide[AppContainer.app_factory]))

    x = get_board_id(project='plat')
    print(f'returned value = {x}')
    _log = MyLogger()
    final_list = get_all_active_versions(start_session('plat'))
    filter_list = ['23.1']
    _result = []

    for f in filter_list:
        if not f.startswith('~'):
            _result += [x for x in final_list if x.startswith(f)]
    for f in filter_list:
        if f.startswith('~'):
            _result_list = [x for x in _result if not x.startswith(f[1:])]

    result_add = []
    result_remove = []
    for f in filter_list:
        if f.startswith('~'):
            result_remove += [x for x in final_list if x.startswith(f[1:])]
        else:
            result_add += [x for x in final_list if x.startswith(f)]

    print(f'Final result 1 = {_result}')
    print(f'Final result 2 = {list(set(result_add) - set(result_remove))}')

    dff1 = report_bug_summary(
        ['22.10.9', '22.10.9.1', '22.10.5', '22.10', '22.10.6', '22.10.4', '22.10.1', '22.10.7', '22.10.2', '22.10.4.2',
         '22.10.3', '22.10.4.3', '22.10.8'],
        # ['23.1', '23.1.2', '23.1.4', '23.1.3', '23.1.4.1', '23.1.1'],
        start_session('plat'), by_fix_version=False
    )

    # dff_pivot = pd.pivot_table(
    #     dff, values=['Internal JIRA#'], index=['Team'], columns=['priority'], aggfunc='count',
    #     fill_value="", margins=True
    # )
    # print(dff1)

    # res = get_user_otp_token('<EMAIL>')
    #
    # print(f'result=|{res}|')
    # update_user_otp_token('<EMAIL>', 'EJ4PUQTTILFEUPJDQWQPMFBVTVWRXAUP')

    # row = get_single_user('<EMAIL>')
    # print(row)
    exit(0)
    user_row = get_single_user('<EMAIL>')
    # print(get_single_user.delete_memoized())

    # get_trial_by_fire()
    check_worklog()

    # rows = get_jira_users_test()
    #
    # df = pd.DataFrame([row.__dict__ for row in rows])
    # df.to_csv("e:/vishal/validate.csv")

    # user_row = get_single_user(None)
    # print(user_row[0][3])

    exit(0)
    execution_time = timeit.timeit(lambda: get_cc_jira_details('Plat_23.1', isbranch=True), number=10)
    print(execution_time)

    execution_time = timeit.repeat(lambda: get_cc_jira_details('Plat_23.1', isbranch=True), repeat=10, number=1)
    print(execution_time)

    # dff = get_issue_epic_mismatch_in_fixversion(['22.7.4', '22.7.5'])
    # dff_match = dff[
    #     dff[["Fix Version", "Epic Fix Version", "Internal JIRA#"]].apply(lambda x: x[0] == x[1], axis=1)].reset_index(
    #     drop=True).copy()

    start_date_ = '2021-10-04'
    end_date_ = '2021-10-21'

    # print("Partial Match --")
    # sort_list()
    # print(dff[dff['AffectsVersion'].str.contains('22.9.1')])
    dff = get_sql_object(['Plat_23.1'], 'app_index', 'plat')
    # dff2 = get_epic_details_in_fixversion(['23.1'])
    dff = get_epic_details_in_fixversion(['22.10.8', '22.11', '22.11.1', '22.11.2', '23.1'])
    dff.to_csv('e:/vishal/sort_out.csv')
    # df_merge = pd.merge(dff2, dff, how="left", left_on='Internal JIRA#', right_on='epic_key')
    # print(df_merge)

    # branch_name = [item for x in get_branch_details('plat') for item in x]
    # branch_name = sorted(
    #     branch_name,
    #     key=lambda item: (int(item.partition(r'[ ._]')[0])
    #                       if item[0].isdigit() else float('inf'), item),
    # )
    # for branch in sort_alpa_numeric(branch_name):
    #     print(branch)
    # print(re.split('([_.])', branch))

    exit(0)
    dff = get_release_defect_status(['22.11', '22.11.1'], 0, 'plat')

    dff.to_csv("e:/vishal/release.csv")
    exit(0)
    # rundetails = []
    # for proj in ['plat', 'cpp']:
    #     df_proj = get_run_details(proj)
    #     df_proj.insert(0, 'project', proj)
    #     rundetails.append(df_proj)
    # dff = pd.concat(rundetails)
    # dff.style.format({'LAST_RUN': lambda t: t.strftime('%m/%d/%Y')})
    # print(dff)

    dff = get_user_detail()
    print(dff)

    print(f'{os.name}, {os.cpu_count()}, {os.environ["NUMBER_OF_PROCESSORS"]}')
    timezone = pytz.timezone("America/Los_Angeles")
    start_date_naive = datetime(2022, 5, 1, 0, 0, 0, 0)

    # start_date_tzaware = datetime(2022, 5, 1, 0, 0, 0, 0, pytz.timezone("America/Los_Angeles"))
    # end_date_tzaware = datetime(2022, 5, 25, 0, 0, 0, 0, pytz.timezone("America/Los_Angeles"))

    start_date_tzaware = datetime(2022, 5, 1, 0, 0, 0, 0, pytz.timezone("UTC"))
    end_date_tzaware = datetime(2022, 6, 14, 0, 0, 0, 0, pytz.timezone("UTC"))
    # start_date_tzaware = timezone.localize(start_date_naive)

    print(f'{start_date_naive} {start_date_tzaware}')
    dff = get_team_members('PLAT_REWARDS', 'plat')
    options = [{'label': row.accountId, 'value': row.displayName} for row in dff.itertuples()]
    print(options)
    print("getting stats")
    dff = get_subtask_issue_description_stats('plat', start_date_tzaware.date(), end_date_tzaware.date())

    today = datetime.today()
    last_friday = datetime.now().date() - timedelta(days=datetime.now().weekday()) + timedelta(days=4, weeks=-1)
    last_sat = datetime.now().date() - timedelta(days=datetime.now().weekday()) + timedelta(days=5, weeks=-2)

    first_day_of_current_month = datetime.now().date().replace(day=1)
    last_month_last_day = first_day_of_current_month - timedelta(days=1)
    start_day_of_prev_month = datetime.today().date().replace(day=1) - timedelta(days=last_month_last_day.day)

    start_time = time.perf_counter_ns()
    dff = get_release_defect_status(
        ['16.00.12', '***********', '***********', '***********', '***********.01', '***********', '***********',
         '***********.01', '***********.03', '***********.02', '***********.03'], 'plat')
    end_time = time.perf_counter_ns()

    print(f"time taken: {(end_time - start_time) * pow(10, -6)} ms")
    dff.to_csv("e:/vishal/release.csv")

    # df_data = dff[['AffectsVersion', 'statusclass', 'key']].groupby(['AffectsVersion', 'statusclass']).agg(
    #     {'key': 'count'}).reset_index()
    #
    # version_list = get_matching_versions('16.00.12%', False, False, 'plat')

    # df = get_release_status_by_issuetype(['16.00.09'])

    # df_2 = get_open_issues('plat')
    # df_2.to_csv("e:/vishal/open_issue.csv")
    # print(df['isSubTask'].unique())
    # print([i for i in df[(df["issuetype"] != 'Epic') & (~df['isSubTask'])]['issuetype'].unique()])
    # dff = df[(df["issuetype"] != 'Epic') & (~df['isSubTask'])]['issuetype'].copy(deep=True)
    # print(datetime.now())
    # x = dff.value_counts().sort_values(ascending=False)
    # print([{'label': f'{i}, {x.get(i)}', 'value': i } for i in x.keys()])
    # for i in x.keys():
    #     print(f'{i}, {x.get(i)}')
    # print(dff.groupby('issuetype').sum().reset_index())
    # dff = get_fixversion_details(['16.00.11.01', '16.00.10.02'])
    # print(dff)

    # dff = get_worklog_details_old(start_date, end_date)
    # dff.to_csv("d:/vishal/worklog.csv")
    # df = get_worklog_details(start_date, end_date)
    # df = df[df['Team Name'].notna()]
    # df.to_csv("d:/vishal/worklog_new.csv")

    # print(get_issue_counts()['initiative'].unique())
    # df = get_versions()
    # print(df[(df['released'] == 0)].shape)
    # print(df.columns)

    # df = get_issue_counts('PLAT')
    # print(df)
    # df_story = df[(df['issuetype'] == 'Story') & (df['sprint'] == 'Sprint 11')].copy(deep=True)
    # df_story.drop(columns=['sprint', 'issuetype', 'components'], inplace=True)
    # df_story = df_story.groupby('statuscategory').sum().reset_index()

    # print(df3.shape)
    # df = get_dev_subtask_details('plat')
    # _df = get_ts_compliance('plat')
    # _df.to_csv("e:/vishal/dump.csv")
    # _df = get_worklog_details(start_date_, end_date_, 'plat')
    # _df.to_csv("e:/vishal/dump_check.csv")
    # _df = get_open_code_reviews('plat')
    # print(_df['affected'].unique())
    # dff = get_version_name([10240, 10250, 10251, 10255, 10256, 10264, 10269, 10270, 10278, 10290, 10295], 'plat')
